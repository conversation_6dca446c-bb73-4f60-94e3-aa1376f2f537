import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:kataria/theme/theme_bloc.dart';
import 'package:kataria/ui/auth/authPage.dart';
import 'package:platform_device_id_plus/platform_device_id.dart';
import 'globle/Constants.dart';
// import 'globle/dependency_injection.dart';
import 'theme/app_theme.dart';
import 'firebase_options.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

// Constants
const _storage = FlutterSecureStorage();
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>(
  debugLabel: "navigator",
);
late AndroidNotificationChannel channel;
late FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin;
bool isFlutterLocalNotificationsInitialized = false;

// Firebase Messaging Background Handler
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  await setupFlutterNotifications();
  debugPrint('Handling a background message ${message.messageId}');
}

// Notification Setup
Future<void> setupFlutterNotifications() async {
  if (isFlutterLocalNotificationsInitialized) return;

  channel = const AndroidNotificationChannel(
    'high_importance_channel',
    'High Importance Notifications',
    description: 'This channel is used for important notifications.',
    importance: Importance.high,
  );

  flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

  await flutterLocalNotificationsPlugin
      .resolvePlatformSpecificImplementation<
        AndroidFlutterLocalNotificationsPlugin
      >()
      ?.createNotificationChannel(channel);

  await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
    alert: true,
    badge: true,
    sound: true,
  );

  await flutterLocalNotificationsPlugin.initialize(
    InitializationSettings(
      android: const AndroidInitializationSettings('launch_background'),
      iOS: const DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      ),
    ),
    onDidReceiveNotificationResponse: _handleNotificationResponse,
  );

  isFlutterLocalNotificationsInitialized = true;
}

void _handleNotificationResponse(NotificationResponse response) async {
  final String? payload = response.payload;
  if (payload?.isNotEmpty ?? false) {
    final list = payload!.split('|');
    debugPrint('Notification payload: ${list.last}');
    navigatorKey.currentState?.pushNamed(payload);
  }
}

void showFlutterNotification(RemoteMessage message) {
  final notification = message.notification;
  if (notification == null || kIsWeb) return;

  flutterLocalNotificationsPlugin.show(
    notification.hashCode,
    notification.title,
    notification.body,
    NotificationDetails(
      android: AndroidNotificationDetails(
        channel.id,
        channel.name,
        channelDescription: channel.description,
        icon: 'launch_background',
      ),
      iOS: const DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      ),
    ),
    payload: "${message.data['id'] ?? ''}|${message.data['type'] ?? ''}",
  );
}

// Firebase Messaging Setup
Future<void> _setupFirebaseMessaging() async {
  if (kIsWeb) return;

  await setupFlutterNotifications();

  if (defaultTargetPlatform == TargetPlatform.iOS) {
    await FirebaseMessaging.instance.requestPermission(
      alert: true,
      badge: true,
      sound: true,
    );

    final String? apnsToken = await FirebaseMessaging.instance.getAPNSToken();
    if (apnsToken == null) {
      debugPrint('Failed to get APNS token');
    } else {
      debugPrint('APNS token: $apnsToken');
    }
  }

  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  try {
    await Future.wait([
      FirebaseMessaging.instance.subscribeToTopic('lead-updates'),
      FirebaseMessaging.instance.subscribeToTopic('news-updates'),
    ]);
  } catch (e) {
    debugPrint('Error subscribing to topics: $e');
  }
}

// Device ID Setup
Future<void> _setupDeviceId() async {
  try {
    String? deviceId = await _storage.read(key: "deviceId");

    if (deviceId == null) {
      try {
        deviceId = await PlatformDeviceId.getDeviceId;
      } on PlatformException {
        deviceId = 'Failed to get deviceId.';
      }
      await _storage.write(key: "deviceId", value: deviceId);
    }

    AppConstants.diviceId = deviceId!;
    debugPrint("Device ID: ${AppConstants.diviceId}");
  } catch (e) {
    debugPrint('Error setting up device ID: $e');
  }
}

// FCM Token Setup
Future<void> _setupFCMToken() async {
  try {
    if (defaultTargetPlatform == TargetPlatform.iOS) {
      await FirebaseMessaging.instance.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );
    }

    final String? fcmToken = await FirebaseMessaging.instance.getToken();
    if (fcmToken?.isNotEmpty ?? false) {
      debugPrint('FCM Token generated successfully: $fcmToken');
      AppConstants.fcmToken = fcmToken!;

      FirebaseMessaging.instance.onTokenRefresh.listen((String token) {
        debugPrint('FCM Token refreshed: $token');
        AppConstants.fcmToken = token;
      });
    } else {
      debugPrint('Failed to generate FCM token - token is null or empty');
    }
  } catch (e) {
    debugPrint('Error generating FCM token: $e');
  }
}

// App Initialization
Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    await _setupFirebaseMessaging();
    // await DependencyInjection.init();
    await GetStorage.init();
    await _setupDeviceId();
    await _setupFCMToken();

    final prefs = GetStorage();
    final String? savedTheme = prefs.read('theme');
    final ThemeMode initialThemeToLoad = savedTheme == 'dark'
        ? ThemeMode.dark
        : ThemeMode.light;

    await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

    runApp(
      MultiBlocProvider(
        providers: [
          BlocProvider<ThemeBloc>(
            create: (context) =>
                ThemeBloc(initialThemeToLoad, prefs)..add(InitialThemeEvent()),
          ),
        ],
        child: const MyApp(),
      ),
    );
  } catch (e) {
    debugPrint('Error during app initialization: $e');
  }
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    _setupMessageHandlers();
  }

  void _setupMessageHandlers() {
    FirebaseMessaging.onMessage.listen(showFlutterNotification);
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      debugPrint('A new onMessageOpenedApp event was published!');
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeMode>(
      builder: (context, state) {
        return GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () => _unfocusCurrentFocus(),
          child: GetMaterialApp(
            title: 'Kataria',
            navigatorKey: navigatorKey,
            debugShowCheckedModeBanner: false,
            theme: lightmode,
            darkTheme: darkmode,
            themeMode: state,
            home: const AuthPage(),
          ),
        );
      },
    );
  }

  void _unfocusCurrentFocus() {
    final FocusScopeNode currentFocus = FocusScope.of(context);
    if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
      FocusManager.instance.primaryFocus?.unfocus();
    }
  }
}
