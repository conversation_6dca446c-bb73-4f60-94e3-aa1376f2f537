import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:kataria/globle/common.dart';
import 'package:kataria/ui/addLead/addLeadPage.dart';
import 'package:kataria/ui/addLead/controller/addLeadController.dart';
import 'package:kataria/ui/addLead/subCompanyPage.dart';

class CompanyPage extends StatelessWidget {
  CompanyPage({super.key});
  Addleadcontroller addleadcontroller = Get.put(Addleadcontroller());
  @override
  Widget build(BuildContext context) {
    addleadcontroller.getCompanyList();
    print(addleadcontroller.hashCode);
    return Scaffold(
      appBar: AppBar(title: const Text("Companies")),
      body: Obx(() {
        return addleadcontroller.isLoading.value
            ? const Center(child: CircularProgressIndicator())
            : addleadcontroller.list.isEmpty
            ? const Center(child: Text('No Companies list found'))
            : courseLayout();
      }),
    );
  }

  Widget courseLayout() {
    return MasonryGridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      mainAxisSpacing: 27,
      crossAxisSpacing: 23,
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      itemCount: addleadcontroller.list.length,
      itemBuilder: (context, index) {
        String imageUrl = "";
        if (addleadcontroller.list[index].logo.isNotEmpty) {
          imageUrl = addleadcontroller.list[index].logo;
          // print(imageUrl);
        }
        return InkWell(
          onTap: () {
            if (addleadcontroller.list[index].subCompanies.isNotEmpty) {
              addleadcontroller.subCompanyList.value =
                  addleadcontroller.list[index].subCompanies;
              Get.to(() => SubCompanyPage());
            } else {
              addleadcontroller.getBranchList();
              addleadcontroller.getProductList(
                addleadcontroller.list[index].id,
              );
              addleadcontroller.incentive.value =
                  addleadcontroller.list[index].docData;
              Get.to(() => const AddLeadPage());
            }
          },
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Card(
              child: CachedNetworkImage(
                imageUrl: imageUrl,
                placeholder: (context, url) =>
                    const Center(child: CircularProgressIndicator()),
                errorWidget: (context, url, error) => const Icon(Icons.error),
              ).hwp(10, 20),
            ),
          ),
        );
      },
    );
  }
}
