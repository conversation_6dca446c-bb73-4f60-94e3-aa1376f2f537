import 'package:kataria/globle/common.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:kataria/apiService/model/productListModel.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../apiService/model/branchListModel.dart';
import '../../globle/Constants.dart';
import '../../globle/myButton.dart';
import 'controller/addLeadController.dart';

class AddLeadPage extends StatefulWidget {
  const AddLeadPage({super.key});

  @override
  State<AddLeadPage> createState() => _AddLeadPageState();
}

class _AddLeadPageState extends State<AddLeadPage> {
  final _formKey = GlobalKey<FormState>();
  TextEditingController nameController = TextEditingController();
  TextEditingController phoneNoController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController branchController = TextEditingController();
  TextEditingController productController = TextEditingController();
  TextEditingController remarkController = TextEditingController();
  late ProductList selProduct;
  late BranchList selBranch = BranchList();
  Addleadcontroller addleadcontroller = Get.find();
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    nameController.dispose();
    phoneNoController.dispose();
    emailController.dispose();
    branchController.dispose();
    productController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Add Lead")),
      body: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            // crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _leadNameWidget().bp(15),
              _leadPhoneNoWidget().bp(15),
              _leadEmailWidget().bp(15),
              _leadBranchWidget().bp(15),
              _leadProductWidget().bp(15),
              _leadNoteWidget().bp(40),
              MyButton(onTap: submitAction, title: "Submit").bp(30),
              if (addleadcontroller.incentive.value != '')
                MyButton(
                  onTap: () async {
                    if (!await launchUrl(
                      mode: LaunchMode.externalApplication,
                      Uri.parse(addleadcontroller.incentive.value),
                    )) {
                      throw Exception(
                        'Could not launch $addleadcontroller.incentive.value',
                      );
                    }
                    // Get.to(() => IncentivePage(
                    //     title: 'Incentive',
                    //     desc: addleadcontroller.incentive.value));
                  },
                  title: "View Incentive Structure",
                ).bp(10),
            ],
          ).allp(20),
        ),
      ),
    );
  }

  Widget _leadNameWidget() {
    return TextFormField(
      controller: nameController,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return "Please enter name of customer.";
        } else if (value.length > 64) {
          return "Maximum 64 characters allowed";
        } else {
          return null;
        }
      },
      keyboardType: TextInputType.emailAddress,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      textInputAction: TextInputAction.next, // Hides the keyboard.
      // style: Theme.of(context).textTheme.subtitle1,
      onChanged: (value) {},
      decoration: const InputDecoration(
        isDense: false,
        counterText: '',
        labelText: "Name of customer",
        prefixIcon: Icon(Icons.person_2_outlined),
      ),
    );
  }

  Widget _leadPhoneNoWidget() {
    return TextFormField(
      controller: phoneNoController,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return "Please enter the phone number of the customer.";
        } else if (value.length > 10) {
          return "Phone number should not exceed 10 characters.";
        } else if (value.length < 10) {
          return "Phone number should be exactly 10 characters.";
        }
        return null;
      },
      keyboardType: TextInputType.number,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      textInputAction: TextInputAction.next,
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      onChanged: (value) {
        // Handle changes if needed
      },
      decoration: const InputDecoration(
        isDense: false,
        counterText: '',
        labelText: "Phone number of customer",
        prefixIcon: Icon(Icons.phone),
      ),
    );
  }

  Widget _leadEmailWidget() {
    return TextFormField(
      controller: emailController,
      validator: (value) {
        String pattern =
            r"[a-zA-Z0-9\+\.\_\%\-\+]{1,256}\@[a-zA-Z0-9]"
            r"[a-zA-Z0-9\-]{0,64}(\.[a-zA-Z0-9][a-zA-Z0-9\-]{0,25})+";
        RegExp regex = RegExp(pattern);
        if (value == null || value.isEmpty) {
          // return "Please enter a email.";
          return null;
        } else if (!regex.hasMatch(value)) {
          return "Please enter a valid email id.";
        } else if (value.length > 64) {
          return "Maximum 64 characters allowed";
        } else {
          return null;
        }
      },
      keyboardType: TextInputType.emailAddress,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      textInputAction: TextInputAction.next, // Hides the keyboard.
      // style: Theme.of(context).textTheme.subtitle1,
      onChanged: (value) {},
      decoration: const InputDecoration(
        isDense: false,
        counterText: '',
        labelText: "Email of customer",
        prefixIcon: Icon(Icons.email_outlined),
      ),
    );
  }

  Widget _leadBranchWidget() {
    return DropdownSearch<BranchList>(
      // validator: (value) {
      //   if (value == null) {
      //     return "Please select branch.";
      //   } else {
      //     return null;
      //   }
      // },
      autoValidateMode: AutovalidateMode.onUserInteraction,
      items: addleadcontroller.branchList,
      itemAsString: (BranchList? branchList) => branchList?.name ?? '',
      onChanged: (BranchList? selectedProduct) {
        if (selectedProduct != null) {
          selBranch = selectedProduct;
        }
      },
      dropdownDecoratorProps: const DropDownDecoratorProps(
        dropdownSearchDecoration: InputDecoration(
          isDense: false,
          counterText: '',
          labelText: "Send lead to branch",
          prefixIcon: Icon(Icons.meeting_room_outlined),
        ),
      ),
      popupProps: PopupProps.menu(
        showSearchBox: true, // Enables the search field
        searchFieldProps: TextFieldProps(
          decoration: InputDecoration(
            labelText: 'Search Branch',
            hintText: 'Type to search',
            prefixIcon: Icon(Icons.search),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(8.0)),
            ),
          ),
        ),
        fit: FlexFit.loose,
        constraints: BoxConstraints(),
      ),
    );
  }

  Widget _leadProductWidget() {
    return DropdownSearch<ProductList>(
      validator: (value) {
        if (value == null) {
          return "Please select a valid product.";
        } else {
          return null;
        }
      },
      autoValidateMode: AutovalidateMode.onUserInteraction,
      items: addleadcontroller.productList,
      itemAsString: (ProductList? product) => product?.name ?? '',
      onChanged: (ProductList? selectedProduct) {
        if (selectedProduct != null) {
          selProduct = selectedProduct;
        }
      },
      dropdownDecoratorProps: const DropDownDecoratorProps(
        dropdownSearchDecoration: InputDecoration(
          isDense: false,
          counterText: '',
          labelText: "Product",
          prefixIcon: Icon(Icons.commute_outlined),
        ),
      ),
      popupProps: const PopupProps.menu(
        fit: FlexFit.loose,
        constraints: BoxConstraints(),
      ),
    );
  }

  // Widget _leadProductWidget() {
  //   return DropdownSearch<ProductList>(
  //     validator: (value) {
  //       if (value == null) {
  //         return "Please select any valid product.";
  //       } else {
  //         return null;
  //       }
  //     },
  //     autoValidateMode: AutovalidateMode.onUserInteraction,
  //     items:addleadcontroller.productList,
  //     dropdownDecoratorProps: const DropDownDecoratorProps(
  //       dropdownSearchDecoration: InputDecoration(
  //         isDense: false,
  //         counterText: '',
  //         labelText: "Product",
  //         prefixIcon: Icon(Icons.commute_outlined)),
  //     ),
  //     popupProps: const PopupProps.menu(
  //         fit: FlexFit.loose, constraints: BoxConstraints()),
  //   );
  //   return TextFormField(
  //       controller: productController,
  //       validator: (value) {
  //          if (value == null || value.isEmpty) {
  //           return "Please select any valid product.";
  //         } else {
  //           return null;
  //         }
  //       },
  //       readOnly: true,
  //       onTap: (){
  //
  //       },
  //       autovalidateMode: AutovalidateMode.onUserInteraction,
  //       textInputAction: TextInputAction.next, // Hides the keyboard.
  //       // style: Theme.of(context).textTheme.subtitle1,
  //       onChanged: (value) {},
  //       decoration: const InputDecoration(
  //           isDense: false,
  //           counterText: '',
  //           labelText: "Product",
  //           prefixIcon: Icon(Icons.commute_outlined)));
  // }

  Widget _leadNoteWidget() {
    return TextFormField(
      controller: remarkController,
      keyboardType: TextInputType.text,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      textInputAction: TextInputAction.next, // Hides the keyboard.
      // style: Theme.of(context).textTheme.subtitle1,
      onChanged: (value) {},
      decoration: const InputDecoration(
        isDense: false,
        counterText: '',
        labelText: "Remarks",
        prefixIcon: Icon(Icons.note_alt_outlined),
      ),
    );
  }

  Future<dynamic> submitAction() async {
    // return;

    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();
      try {
        Map<String, dynamic> map = {};
        map['name'] = nameController.text;
        map['phone'] = phoneNoController.text;
        map['email'] = emailController.text;
        map['remarks'] = remarkController.text;
        map['product'] = selProduct.id;
        map['destinationBranch'] = selBranch.id ?? '';
        debugPrint(map.toString());
        await Future.delayed(const Duration(seconds: 1));
        addleadcontroller.submitNewLead(map, context);
      } catch (e) {
        Get.defaultDialog(
          content: Text(e.toString()),
          textConfirm: 'OK',
          titleStyle: const TextStyle(fontSize: 0),
          middleText: "",
          buttonColor: AppColors.primaryElement,
          confirmTextColor: Colors.black,
          onConfirm: () {
            Get.back();
          },
        );
      }
    }
  }
}
