import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../apiService/api_response.dart';
import '../../../apiService/jsonPlacehoderProvider.dart';
import '../../../apiService/model/branchListModel.dart';
import '../../../apiService/model/companyListModel.dart';
import '../../../apiService/model/productListModel.dart';
import '../../../globle/Constants.dart';
import '../../leadStatus/leadStatusPage.dart';

class Addleadcontroller extends GetxController {
  final JsonPlaceholerProvider _repository = JsonPlaceholerProvider();
  var list = <CompanyList>[].obs;
  var subCompanyList = <SubCompanyList>[].obs;
  var branchList = <BranchList>[].obs;
  var productList = <ProductList>[].obs;
  RxBool isLoading = false.obs;
  RxString incentive = ''.obs;

  getCompanyList() async {
    try {
      isLoading(true);
      ApiResponse<dynamic> model = await _repository.getCompanyList();
      if (model.status == Status.COMPLETED) {
        CompanyListModel profileDetailsModel =
            CompanyListModel.fromJson(model.data);
        list.value = profileDetailsModel.data ?? [];
        // print(list);
      }
    } catch (e) {
      print('Error while getting data is $e');
    } finally {
      isLoading(false);
    }
  }

  getBranchList() async {
    ApiResponse<dynamic> model = await _repository.getBranchList();
    if (model.status == Status.COMPLETED) {
      BranchListModel profileDetailsModel =
          BranchListModel.fromJson(model.data);
      branchList.value = profileDetailsModel.data ?? [];
      print(branchList);
    }
  }

  getProductList(String cId) async {
    ApiResponse<dynamic> model = await _repository.getProductList(cId);
    if (model.status == Status.COMPLETED) {
      ProductListModel profileDetailsModel =
          ProductListModel.fromJson(model.data);
      productList.value = profileDetailsModel.data ?? [];
      print(productList);
    }
  }

  submitNewLead(Map<String, dynamic> map,BuildContext context) async {
    ApiResponse<dynamic> model = await _repository.submitNewLeadAction(map);
    if (model.status == Status.COMPLETED) {
      // Get.defaultDialog(
      //     content: Text(model.data['message'] ?? ''),
      //     textConfirm: 'OK',
      //     titleStyle: const TextStyle(fontSize: 0),
      //     middleText: "",
      //     buttonColor: AppColors.primaryElement,
      //     confirmTextColor: Colors.black,
      //     onConfirm: () {
      //       Get.back();
            Navigator.of(context).popUntil((route) => route.isFirst);
            Get.to(() => LeadStatusPage());
      //     });
      // Get.snackbar('', model.data['message'] ?? '');
    } else {
      Get.defaultDialog(
          content: Text(model.message ?? ''),
          textConfirm: 'OK',
          titleStyle: const TextStyle(fontSize: 0),
          middleText: "",
          buttonColor: AppColors.primaryElement,
          confirmTextColor: Colors.black,
          onConfirm: () {
            Get.back();
          });
      // Get.snackbar('error', model.message ?? '');
    }
  }
}
