
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';

class IncentivePage extends StatefulWidget {
  IncentivePage({super.key, required this.title,required this.desc});
  String title;
  String desc;

  @override
  State<IncentivePage> createState() => _IncentivePageState();
}

class _IncentivePageState extends State<IncentivePage> {
  late final WebViewController _controller;
  bool isLoading = true; // Tracks the loading state
  // @override
  // void initState() {
  //   super.initState();
  //   _controller = WebViewController()
  //     ..setJavaScriptMode(JavaScriptMode.unrestricted)
  //    ..setBackgroundColor(Theme.of(context).brightness == Brightness.dark ? Colors.black : Colors.white)
  //     ..loadHtmlString(widget.desc);
  // }
  @override
  void initState() {
    super.initState();

    // Initialize the WebViewController without context-dependent operations
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.white)
      ..setNavigationDelegate(NavigationDelegate(
        onPageStarted: (_) {
          setState(() {
            isLoading = true;
          });
        },
        onPageFinished: (_) {
          setState(() {
            isLoading = false;
          });
        },
        onWebResourceError: (error) {
          setState(() {
            isLoading = false;
          });
        },
        onNavigationRequest: (NavigationRequest request) {
          if (request.url.startsWith("http://") || request.url.startsWith("https://")) {
            // Optionally open external browser for external links
            // if (request.url.contains("some-condition")) {
              // Example condition to intercept specific URLs
              // Open with an external browser
              launchUrl(Uri.parse(request.url), mode: LaunchMode.externalApplication);
              return NavigationDecision.prevent;
            // }
            // return NavigationDecision.navigate; // Allow navigation for others
          } else {
            // Prevent navigation for unsupported schemes
            return NavigationDecision.prevent;
          }
        },
      ))
      ..loadHtmlString(widget.desc);

    // Use a post-frame callback to access the theme safely
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    //   _controller.setBackgroundColor(Colors.white);
    // });
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
      ),
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(20.0),
            child: WebViewWidget(controller: _controller),
          ),
          if (isLoading)
            Center(
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }
}
