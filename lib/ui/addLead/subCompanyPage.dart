import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:kataria/globle/common.dart';
import 'package:kataria/ui/addLead/addLeadPage.dart';
import 'controller/addLeadController.dart';

class SubCompanyPage extends StatelessWidget {
  SubCompanyPage({super.key});
  Addleadcontroller addleadcontroller = Get.find();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Sub Companies")),
      body: courseLayout(),
    );
  }

  Widget courseLayout() {
    return MasonryGridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      mainAxisSpacing: 27,
      crossAxisSpacing: 23,
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      itemCount: addleadcontroller.subCompanyList.length,
      itemBuilder: (context, index) {
        String imageUrl = "";
        if (addleadcontroller.subCompanyList[index].logo.isNotEmpty) {
          imageUrl = addleadcontroller.subCompanyList[index].logo;
          // print(imageUrl);
        }
        return InkWell(
          onTap: () {
            addleadcontroller.getBranchList();
            addleadcontroller.getProductList(
              addleadcontroller.subCompanyList[index].id,
            );
            addleadcontroller.incentive.value =
                addleadcontroller.subCompanyList[index].docData;
            Get.to(() => const AddLeadPage());
          },
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Card(
              child: CachedNetworkImage(
                imageUrl: imageUrl,
                placeholder: (context, url) =>
                    const Center(child: CircularProgressIndicator()),
                errorWidget: (context, url, error) => const Icon(Icons.error),
              ).hwp(10, 20),
            ),
          ),
        );
      },
    );
  }
}
