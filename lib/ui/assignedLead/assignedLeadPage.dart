import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kataria/apiService/api_response.dart';
import 'package:kataria/apiService/model/leadListModel.dart';
import 'package:kataria/globle/Constants.dart';
import 'package:kataria/globle/common.dart';
import 'package:kataria/globle/myButton.dart';
import 'package:kataria/ui/assignedLead/controller/assignedLeadController.dart';

class MyAssignedLeadsPage extends StatelessWidget {
  MyAssignedLeadsPage({super.key});
  AssignedLeadController assignedLeadController = Get.put(
    AssignedLeadController(),
  );
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("My Assigned Leads")),
      body: Obx(() {
        return assignedLeadController.isLoading.value
            ? const Center(child: CircularProgressIndicator())
            : assignedLeadController.list.isEmpty
            ? const Center(child: Text('No any assigned leads'))
            : ListView.builder(
                itemCount: assignedLeadController.list.length,
                padding: const EdgeInsets.symmetric(vertical: 15),
                itemBuilder: (BuildContext context, int index) {
                  LeadList obj = assignedLeadController.list[index];
                  String imageUrl = "";
                  if (obj.logo != '') {
                    imageUrl = obj.logo!;
                    // print(imageUrl);
                  }
                  String color = '0xFF${obj.color}';
                  return Container(
                    margin: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 10,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(color: Color(int.parse(color))),
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            CachedNetworkImage(
                              width: Get.width / 4,
                              imageUrl: imageUrl,
                              fit: BoxFit.fitWidth,
                              placeholder: (context, url) => const Center(
                                child: CircularProgressIndicator(),
                              ),
                              errorWidget: (context, url, error) =>
                                  const Icon(Icons.error),
                            ).paddingAll(10),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: [
                                  Text(
                                    obj.name?.capitalize ?? '',
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w800,
                                      color: Colors.black,
                                    ),
                                  ).bp(10),
                                  Text(
                                    obj.phone ?? '',
                                    style: const TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w200,
                                      color: Colors.blueGrey,
                                    ),
                                  ),
                                  Text(
                                    obj.email ?? '',
                                    style: const TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w200,
                                      color: Colors.blueGrey,
                                    ),
                                  ),
                                  if (obj.remarks != '')
                                    Text(
                                      'Remarks: ${obj.remarks}',
                                      style: const TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w200,
                                        color: Colors.black,
                                      ),
                                    ),
                                  Row(
                                    children: [
                                      const Text(
                                        'Status: ',
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w200,
                                          color: Colors.black,
                                        ),
                                      ),
                                      Text(
                                        obj.statusEnum ?? '',
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w800,
                                          color: Color(int.parse(color)),
                                        ),
                                      ),
                                    ],
                                  ).bp(10),
                                  SizedBox(
                                    height: 40,
                                    child: MyButton(
                                      onTap: () async {
                                        assignedLeadController
                                                .remarksController
                                                .text =
                                            obj.remarks ?? '';
                                        assignedLeadController
                                                .dmsNumberController
                                                .text =
                                            obj.dmsNumber ?? '';
                                        // Get.dialog(
                                        //   AlertDialog(
                                        //     // title: Text("Remarks"),
                                        //     content: Form(
                                        //       key: assignedLeadController
                                        //           .formKey,
                                        //       child: Column(
                                        //         crossAxisAlignment: CrossAxisAlignment.start,
                                        //         mainAxisSize:
                                        //             MainAxisSize.min,
                                        //         children: <Widget>[
                                        //           // Text('DMS Number: ${obj.dmsNumber}'),
                                        //           // Text('Remark'),
                                        //           TextFormField(
                                        //             controller:
                                        //                 assignedLeadController
                                        //                     .dmsNumberController,
                                        //             validator:
                                        //                 (final String?
                                        //                     value) {
                                        //               if (value == null ||
                                        //                   value.isEmpty) {
                                        //                 return 'Please enter DMS Number.';
                                        //               }
                                        //               return null; // Input is valid
                                        //             },
                                        //             autovalidateMode:
                                        //                 AutovalidateMode
                                        //                     .onUserInteraction,
                                        //             cursorColor:
                                        //                 Colors.black,
                                        //             keyboardType:
                                        //                 TextInputType
                                        //                     .streetAddress,
                                        //             textInputAction:
                                        //                 TextInputAction
                                        //                     .done,
                                        //             decoration:
                                        //                 InputDecoration(
                                        //               hintText: 'DMS Inquiry Number',
                                        //                   labelText: 'DMS Inquiry Number'
                                        //             ),
                                        //           ).bp(10),
                                        //           TextFormField(
                                        //             controller:
                                        //                 assignedLeadController
                                        //                     .remarksController,
                                        //             // validator:
                                        //             //     (final String?
                                        //             //         value) {
                                        //             //   if (value == null ||
                                        //             //       value.isEmpty) {
                                        //             //     return 'Please enter remarks.';
                                        //             //   }
                                        //             //   return null; // Input is valid
                                        //             // },
                                        //             autovalidateMode:
                                        //                 AutovalidateMode
                                        //                     .onUserInteraction,
                                        //             cursorColor:
                                        //                 Colors.black,
                                        //             keyboardType:
                                        //                 TextInputType
                                        //                     .streetAddress,
                                        //             textInputAction:
                                        //                 TextInputAction
                                        //                     .done,
                                        //             decoration:
                                        //                 InputDecoration(
                                        //               hintText: 'Remarks',
                                        //                   labelText: 'Remarks'
                                        //             ),
                                        //           ),
                                        //         ],
                                        //       ),
                                        //     ),
                                        //     actions: <Widget>[
                                        //       MyButton(
                                        //         onTap: () async {
                                        //           if (assignedLeadController
                                        //                   .formKey
                                        //                   .currentState
                                        //                   ?.validate() ??
                                        //               false) {
                                        //             final Map<String,
                                        //                     dynamic> map =
                                        //                 <String, dynamic>{};
                                        //             map['dmsNumber'] =
                                        //                 assignedLeadController
                                        //                     .dmsNumberController
                                        //                     .text;
                                        //             map['remarks'] =
                                        //                 assignedLeadController
                                        //                     .remarksController
                                        //                     .text;
                                        //             map['id'] =
                                        //                 obj.id ?? '';
                                        //             map['status'] =
                                        //                 obj.statusEnum ?? '';
                                        //
                                        //             //     debugPrint(map.toString());
                                        //             Get.back(); // Close the dialog
                                        //             ApiResponse<dynamic>  response =
                                        //                 await assignedLeadController
                                        //                     .addRemarksAction(
                                        //                         map);
                                        //             // print(response);
                                        //             if (response.status == Status.COMPLETED) {
                                        //               obj.dmsNumber = assignedLeadController
                                        //                   .dmsNumberController
                                        //                   .text;
                                        //               obj.remarks = assignedLeadController
                                        //                   .remarksController
                                        //                   .text;
                                        //               assignedLeadController
                                        //                   .remarksController
                                        //                   .text = '';
                                        //               assignedLeadController
                                        //                   .dmsNumberController
                                        //                   .text = '';
                                        //               Get.defaultDialog(
                                        //                 title: "",
                                        //                 middleText: response.data.message ?? '',
                                        //                 textConfirm: "OK",
                                        //                 buttonColor: AppColors.primaryElement,
                                        //                 titleStyle: const TextStyle(fontSize: 0),
                                        //                 onConfirm: () => Get.back(),
                                        //                 // textCancel: "Cancel",
                                        //               );
                                        //             }else{
                                        //               Get.defaultDialog(
                                        //                 title: "",
                                        //                 middleText: response.message ?? '',
                                        //                 textConfirm: "OK",
                                        //                 buttonColor: AppColors.primaryElement,
                                        //                 titleStyle: const TextStyle(fontSize: 0),
                                        //                 onConfirm: () => Get.back(),
                                        //                 // textCancel: "Cancel",
                                        //               );
                                        //             }
                                        //           } // Close the dialog
                                        //         },
                                        //         title:'Update',
                                        //       ),
                                        //     ],
                                        //   ),
                                        // );
                                        Get.dialog(
                                          AlertDialog(
                                            content: SizedBox(
                                              width: Get.width,
                                              child: Form(
                                                key: assignedLeadController
                                                    .formKey,
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  children: <Widget>[
                                                    buildInputField(
                                                      controller:
                                                          assignedLeadController
                                                              .dmsNumberController,
                                                      hintText:
                                                          'DMS Inquiry Number',
                                                      labelText:
                                                          'DMS Inquiry Number',
                                                    ).vp(
                                                      15,
                                                    ), // Assuming .bp(10) is custom padding
                                                    buildInputField(
                                                      controller:
                                                          assignedLeadController
                                                              .remarksController,
                                                      hintText: 'Remarks',
                                                      labelText: 'Remarks',
                                                      maxLines: 4,
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            actions: <Widget>[
                                              MyButton(
                                                onTap: () async {
                                                  if (assignedLeadController
                                                          .formKey
                                                          .currentState
                                                          ?.validate() ??
                                                      false) {
                                                    final Map<String, dynamic>
                                                    map = {
                                                      'dmsNumber':
                                                          assignedLeadController
                                                              .dmsNumberController
                                                              .text,
                                                      'remarks':
                                                          assignedLeadController
                                                              .remarksController
                                                              .text,
                                                      'id': obj.id ?? '',
                                                      'status':
                                                          obj.statusEnum ?? '',
                                                    };
                                                    Get.back(); // Close the dialog
                                                    final response =
                                                        await assignedLeadController
                                                            .addRemarksAction(
                                                              map,
                                                            );
                                                    if (response.status ==
                                                        Status.COMPLETED) {
                                                      obj.dmsNumber =
                                                          map['dmsNumber'];
                                                      obj.remarks =
                                                          map['remarks'];
                                                      assignedLeadController
                                                          .dmsNumberController
                                                          .clear();
                                                      assignedLeadController
                                                          .remarksController
                                                          .clear();
                                                      Get.defaultDialog(
                                                        title: "",
                                                        middleText:
                                                            response
                                                                .data
                                                                .message ??
                                                            '',
                                                        textConfirm: "OK",
                                                        buttonColor: AppColors
                                                            .primaryElement,
                                                        titleStyle:
                                                            const TextStyle(
                                                              fontSize: 0,
                                                            ),
                                                        onConfirm: () =>
                                                            Get.back(),
                                                      );
                                                    } else {
                                                      Get.snackbar(
                                                        'Error',
                                                        response.message ??
                                                            'Something went wrong.',
                                                        snackPosition:
                                                            SnackPosition
                                                                .BOTTOM,
                                                        backgroundColor:
                                                            Colors.red,
                                                        colorText: Colors.white,
                                                      );
                                                    }
                                                  }
                                                },
                                                title: 'Update',
                                              ),
                                            ],
                                          ),
                                        );
                                      },
                                      title: 'Update Lead',
                                    ),
                                  ),
                                ],
                              ).paddingOnly(left: 20, right: 10, top: 15),
                            ),
                          ],
                        ),
                        Container(
                          margin: const EdgeInsets.only(top: 10),
                          padding: const EdgeInsets.all(10),
                          color: Color(int.parse(color)),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                obj.product ?? '',
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w700,
                                  color: Colors.white,
                                ),
                              ),
                              if (obj.empName != '')
                                RichText(
                                  text: TextSpan(
                                    text: obj.empName ?? '',
                                    style: const TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.white,
                                    ),
                                    children: [
                                      if (obj.empBranch != '')
                                        TextSpan(
                                          text:
                                              ' (${obj.salesExecutiveBranch})',
                                          style: const TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                            color: Colors.white,
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              );
      }),
    );
  }

  Widget buildInputField({
    required TextEditingController controller,
    required String hintText,
    required String labelText,
    int maxLines = 1,
  }) {
    return TextFormField(
      controller: controller,
      maxLines: maxLines,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      cursorColor: Colors.black,
      keyboardType: maxLines > 1
          ? TextInputType.multiline
          : TextInputType.streetAddress,
      decoration: InputDecoration(
        hintText: hintText,
        labelText: labelText,
        alignLabelWithHint: maxLines > 1,
      ),
    );
  }
}
