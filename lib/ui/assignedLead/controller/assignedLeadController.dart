import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../apiService/api_response.dart';
import '../../../apiService/jsonPlacehoderProvider.dart';
import '../../../apiService/model/leadListModel.dart';

class AssignedLeadController extends GetxController {
  final JsonPlaceholerProvider _repository = JsonPlaceholerProvider();
  var list = <LeadList>[].obs;
  RxBool isLoading = false.obs;
  final TextEditingController remarksController = TextEditingController();
  final TextEditingController dmsNumberController = TextEditingController();
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    getLeadList();
  }

  getLeadList() async {
    try{
      isLoading(true);
      ApiResponse<dynamic> model = await _repository.getAssignedLeadList();
      if (model.status == Status.COMPLETED) {
        LeadListModel leadListModel = LeadListModel.fromJson(model.data);
        list.value = leadListModel.leadList ?? [];
        print(leadListModel);
      }
    } catch (e) {
      print('Error while getting data is $e');
    } finally {
      isLoading(false);
    }
  }
  Future<dynamic> addRemarksAction(final Map<String, dynamic> data) async {
    try {
      final response = await _repository.addRemarks(data);
      getLeadList();
      return response;
    }finally{
      // Loader.load(false);
    }
  }
}
