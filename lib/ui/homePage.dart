import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:kataria/globle/common.dart';
import 'package:kataria/globle/text_style.dart';
import 'package:kataria/ui/assignedLead/assignedLeadPage.dart';
import 'package:kataria/ui/addLead/companyPage.dart';
import 'package:kataria/ui/leadStatus/leadStatusPage.dart';
import 'package:kataria/ui/notification/notificationPage.dart';
import '../globle/Constants.dart';
import '../globle/logoutBtn.dart';
import '../theme/theme_bloc.dart';
import 'announcements/announcementPage.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        actions: [
          BlocBuilder<ThemeBloc, ThemeMode>(
            builder: (context, state) {
              return Switch(
                // This bool value toggles the switch.
                value: state == ThemeMode.dark,
                // activeColor: Colors.red,
                onChanged: (bool value) {
                  // This is called when the user toggles the switch.
                  context.read<ThemeBloc>().add(ToggleThemeEvent());
                },
              );
            },
          ),
          const LogoutBtn(),
        ],
        leading: IconButton(
          icon: const Icon(Icons.notifications),
          onPressed: () {
            Get.to(() => NotificationPage());
          },
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Welcome',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.headlineLarge,
            ).tp(20),
            Text(
              AppConstants.currentProfile.username ?? '',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.titleMedium!,
            ).bp(0),
            Text(
              'Employee Code: ${AppConstants.currentProfile.empCode}',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodySmall!,
            ).bp(0),
            Text(
              'City: ${AppConstants.currentProfile.city}',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodySmall!,
            ).bp(0),
            Text(
              'Branch: ${AppConstants.currentProfile.branch}',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodySmall!,
            ).bp(50),
            InkWell(
              onTap: () {
                Get.to(() => CompanyPage());
              },
              child: Column(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: const Color.fromRGBO(232, 190, 73, 1),
                    // backgroundImage: AssetImage('assets/images/addLead.png',),
                    child: Image.asset('assets/images/addLead.png', height: 30),
                  ).bp(5),
                  Text(
                    'Add New Lead',
                    textAlign: TextAlign.center,
                    style: AppTextStyles.medium(),
                  ),
                ],
              ),
            ).bp(30),
            InkWell(
              onTap: () {
                Get.to(() => LeadStatusPage());
              },
              child: Column(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: const Color.fromRGBO(232, 190, 73, 1),
                    // backgroundImage: AssetImage('assets/images/addLead.png',),
                    child: Image.asset(
                      'assets/images/leadStatus.png',
                      height: 30,
                    ),
                  ).bp(5),
                  Text(
                    'Lead Status',
                    textAlign: TextAlign.center,
                    style: AppTextStyles.medium(),
                  ),
                ],
              ),
            ).bp(30),
            InkWell(
              onTap: () {
                Get.to(() => MyAssignedLeadsPage());
              },
              child: Column(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: const Color.fromRGBO(232, 190, 73, 1),
                    // backgroundImage: AssetImage('assets/images/addLead.png',),
                    child: Image.asset(
                      'assets/images/assignedLead.png',
                      height: 30,
                    ),
                  ).bp(5),
                  Text(
                    'My Assigned Leads',
                    textAlign: TextAlign.center,
                    style: AppTextStyles.medium(),
                  ),
                ],
              ),
            ).bp(30),
            InkWell(
              onTap: () {
                Get.to(() => AnnouncementPage());
              },
              child: Column(
                children: [
                  const CircleAvatar(
                    radius: 30,
                    backgroundColor: Color.fromRGBO(232, 190, 73, 1),
                    // backgroundImage: AssetImage('assets/images/addLead.png',),
                    child: Icon(
                      Icons.campaign_outlined,
                      size: 30,
                      color: Colors.black,
                    ),
                  ).bp(5),
                  Text(
                    'Announcements',
                    textAlign: TextAlign.center,
                    style: AppTextStyles.medium(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
