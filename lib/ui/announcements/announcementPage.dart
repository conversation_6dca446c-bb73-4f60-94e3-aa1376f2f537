import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:kataria/globle/common.dart';
import 'package:kataria/ui/announcements/controller/announcementController.dart';

import '../../apiService/model/announcementListModel.dart';
import '../addLead/incentivePage.dart';

class AnnouncementPage extends StatelessWidget {
  AnnouncementPage({super.key});
  AnnouncementController announcementController = Get.put(
    AnnouncementController(),
  );
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Announcements')),
      body: Obx(() {
        return announcementController.isLoading.value
            ? const Center(child: CircularProgressIndicator())
            : announcementController.list.isEmpty
            ? const Center(child: Text('No any announcements found'))
            : _announcementLayout().vp(20);
      }),
    );
  }

  Widget _announcementLayout() {
    return ListView.separated(
      itemCount: announcementController.list.length,
      itemBuilder: (BuildContext context, int index) {
        AnnouncementList obj = announcementController.list[index];
        return InkWell(
          child: ListTile(
            title: Text(removeHtmlTags(obj.title ?? '')).hp(20),
            subtitle: obj.updatedAt == null
                ? null
                : Text(
                    "     ${DateFormat('dd MMM yyyy hh:mm').format(obj.updatedAt!)}",
                    style: Theme.of(context).textTheme.bodySmall,
                  ).tp(5),
            trailing: const Icon(Icons.arrow_forward_ios_outlined),
          ),
          onTap: () {
            Get.to(
              () => IncentivePage(
                title: 'Announcement',
                desc: "${obj.title ?? ''}<br><br>${obj.description ?? ''}",
              ),
            );
            // Get.to(() => AnnouncementDetailsPage(obj: obj));
          },
        );
      },
      separatorBuilder: (BuildContext context, int index) {
        return Divider(color: Colors.grey.shade200);
      },
    );
  }

  String removeHtmlTags(String htmlString) {
    final RegExp regExp = RegExp(
      r'<[^>]*>',
      multiLine: true,
      caseSensitive: false,
    );
    return htmlString.replaceAll(regExp, '');
  }
}
