import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:kataria/globle/common.dart';

import '../../apiService/model/announcementListModel.dart';

class AnnouncementDetailsPage extends StatelessWidget {
  AnnouncementDetailsPage({super.key, required this.obj});
  AnnouncementList obj;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Announcement')),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          HtmlWidget(obj.title ?? '').bp(20),
          HtmlWidget(obj.description ?? ''),
        ],
      ).allp(20),
    );
  }
}
