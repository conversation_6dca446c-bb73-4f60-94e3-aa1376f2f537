import 'package:get/get.dart';

import '../../../apiService/api_response.dart';
import '../../../apiService/jsonPlacehoderProvider.dart';
import '../../../apiService/model/announcementListModel.dart';

class NotificationController extends GetxController {
  final JsonPlaceholerProvider _repository = JsonPlaceholerProvider();
  var list = <AnnouncementList>[].obs;
  RxBool isLoading = false.obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    getNotificationList();
  }

  getNotificationList() async {
    try {
      isLoading(true);
      ApiResponse<dynamic> model = await _repository.getNotificationList();
      print(model.data);
      if (model.status == Status.COMPLETED) {
        AnnouncementListModel profileDetailsModel =
        AnnouncementListModel.fromJson(model.data);
        list.value = profileDetailsModel.data ?? [];
        print(list);
      }
    } catch (e) {
      print('Error while getting data is $e');
    } finally {
      isLoading(false);
    }
  }
}
