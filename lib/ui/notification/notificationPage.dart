import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kataria/globle/common.dart';
import 'package:kataria/ui/assignedLead/assignedLeadPage.dart';
import 'package:kataria/ui/notification/controller/notificationController.dart';
import 'package:intl/intl.dart';
import '../../apiService/model/announcementListModel.dart';
import '../addLead/incentivePage.dart';

class NotificationPage extends StatelessWidget {
  NotificationPage({super.key});
  NotificationController notificationController = Get.put(
    NotificationController(),
  );
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Notification')),
      body: Obx(() {
        return notificationController.isLoading.value
            ? const Center(child: CircularProgressIndicator())
            : notificationController.list.isEmpty
            ? const Center(child: Text('No any notification found'))
            : _announcementLayout().vp(20);
      }),
    );
  }

  Widget _announcementLayout() {
    return ListView.separated(
      itemCount: notificationController.list.length,
      itemBuilder: (BuildContext context, int index) {
        AnnouncementList obj = notificationController.list[index];
        return InkWell(
          child: ListTile(
            title: Text(removeHtmlTags(obj.title ?? '')).hp(20),
            subtitle: Text(
              "     ${DateFormat('dd MMM yyyy hh:mm').format(obj.updatedAt!)}",
              style: Theme.of(context).textTheme.bodySmall,
            ).tp(5),
            trailing: const Icon(Icons.arrow_forward_ios_outlined),
          ),
          onTap: () {
            //
            if (obj.type == 'Announcement') {
              Get.to(
                () => IncentivePage(
                  title: 'Announcement',
                  desc: "${obj.title ?? ''}<br><br>${obj.description ?? ''}",
                ),
              );
            } else {
              Get.to(() => MyAssignedLeadsPage());
            }
          },
        );
      },
      separatorBuilder: (BuildContext context, int index) {
        return const Divider();
      },
    );
  }

  String removeHtmlTags(String htmlString) {
    final RegExp regExp = RegExp(
      r'<[^>]*>',
      multiLine: true,
      caseSensitive: false,
    );
    return htmlString.replaceAll(regExp, '');
  }
}
