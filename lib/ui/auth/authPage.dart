import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../apiService/authentication_manager.dart';
import '../homePage.dart';
import 'loginPage.dart';

class AuthPage extends StatefulWidget {
  const AuthPage({super.key});

  @override
  State<AuthPage> createState() => _AuthPageState();
}

class _AuthPageState extends State<AuthPage> {
  final AuthenticationManager _authmanager = Get.put(AuthenticationManager());
  bool _hasError = false;
  String? _errorMessage;

  Future<void> initializeSettings() async {
    try {
      await Future.delayed(const Duration(seconds: 2)); // Minimum splash time
      await _authmanager.checkLoginStatus();
    } catch (e) {
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = 'Failed to initialize app. Please try again.';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: FutureBuilder<void>(
        future: initializeSettings(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting &&
              !_hasError) {
            return waitingView();
          } else if (_hasError) {
            return errorView();
          } else {
            return Obx(() {
              return _authmanager.isLogged.value
                  ? const HomePage()
                  : const LoginPage();
            });
          }
        },
      ),
      // body: StreamBuilder<User?>(
      //     stream: FirebaseAuth.instance.authStateChanges(),
      //     builder: (context, snapshot) {
      //       if (snapshot.hasData) {
      //         return const HomePage();
      //       } else {
      //         return const LoginPage();
      //       }
      //     }),
    );
  }

  Scaffold waitingView() {
    return const Scaffold(
      body: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.all(16.0),
              child: CircularProgressIndicator(),
            ),
            Text('Loading...'),
          ],
        ),
      ),
    );
  }

  Scaffold errorView() {
    return Scaffold(
      body: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 48, color: Colors.red),
            const SizedBox(height: 16),
            Text(_errorMessage ?? 'An error occurred'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _hasError = false;
                  _errorMessage = null;
                });
                initializeSettings();
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }
}
