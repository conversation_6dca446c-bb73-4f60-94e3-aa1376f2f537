import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kataria/globle/common.dart';
import '../../apiService/api_response.dart';
import '../../apiService/jsonPlacehoderProvider.dart';
import '../../globle/Constants.dart';
import '../../globle/myButton.dart';

class SignUpPage extends StatefulWidget {
  const SignUpPage({super.key});

  @override
  State<SignUpPage> createState() => _SignUpPageState();
}

class _SignUpPageState extends State<SignUpPage> {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController mobileController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController cityController = TextEditingController();
  final TextEditingController carInfoController = TextEditingController();

  final JsonPlaceholerProvider _repository = JsonPlaceholerProvider();
  final _formKey = GlobalKey<FormState>();

  bool hasVehicle = false;
  bool isLoading = false;

  @override
  void dispose() {
    nameController.dispose();
    mobileController.dispose();
    emailController.dispose();
    cityController.dispose();
    carInfoController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Sign Up',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        iconTheme: IconThemeData(
          color: Theme.of(context).textTheme.bodyLarge?.color,
        ),
      ),
      body: SingleChildScrollView(
        child: SafeArea(
          child: Form(
            key: _formKey,
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Image.asset("assets/images/logo.png", height: 150).vp(0),
                  Text(
                    "Create Account",
                    style: Theme.of(context).textTheme.headlineLarge,
                  ),
                  Text(
                    "Sign up to get started with our services",
                    style: Theme.of(context).textTheme.titleMedium,
                  ).bp(30),

                  _buildNameField().bp(20),
                  _buildMobileField().bp(20),
                  _buildEmailField().bp(20),
                  _buildCityField().bp(20),
                  _buildVehicleQuestion().bp(20),

                  if (hasVehicle) _buildCarInfoField().bp(20),

                  const SizedBox(height: 30),
                  Center(
                    child: isLoading
                        ? const CircularProgressIndicator()
                        : MyButton(onTap: signUpAction, title: "Sign Up"),
                  ).bp(20),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNameField() {
    return TextFormField(
      controller: nameController,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return "Please enter your name";
        }
        return null;
      },
      keyboardType: TextInputType.text,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      textInputAction: TextInputAction.next,
      decoration: const InputDecoration(
        labelText: "Full Name",
        prefixIcon: Icon(Icons.person),
      ),
    );
  }

  Widget _buildMobileField() {
    return TextFormField(
      controller: mobileController,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return "Please enter your mobile number";
        } else if (value.length != 10) {
          return "Mobile number should be exactly 10 digits";
        }
        return null;
      },
      keyboardType: TextInputType.phone,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      textInputAction: TextInputAction.next,
      maxLength: 10,
      decoration: const InputDecoration(
        labelText: "Mobile Number",
        prefixIcon: Icon(Icons.phone),
        counterText: '',
      ),
    );
  }

  Widget _buildEmailField() {
    return TextFormField(
      controller: emailController,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return "Please enter your email";
        } else if (!GetUtils.isEmail(value)) {
          return "Please enter a valid email address";
        }
        return null;
      },
      keyboardType: TextInputType.emailAddress,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      textInputAction: TextInputAction.next,
      decoration: const InputDecoration(
        labelText: "Email Address",
        prefixIcon: Icon(Icons.email),
      ),
    );
  }

  Widget _buildCityField() {
    return TextFormField(
      controller: cityController,
      validator: (value) {
        // if (value == null || value.isEmpty) {
        //   return "Please enter your city";
        // }
        return null;
      },
      keyboardType: TextInputType.text,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      textInputAction: TextInputAction.next,
      decoration: const InputDecoration(
        labelText: "City",
        prefixIcon: Icon(Icons.location_city),
      ),
    );
  }

  Widget _buildVehicleQuestion() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Do you own any vehicle sold by Kataria?",
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w500),
        ).bp(10),
        Row(
          children: [
            Expanded(
              child: RadioListTile<bool>(
                title: const Text("Yes"),
                value: true,
                groupValue: hasVehicle,
                onChanged: (bool? value) {
                  setState(() {
                    hasVehicle = value ?? false;
                    if (!hasVehicle) {
                      carInfoController.clear();
                    }
                  });
                },
                activeColor: AppColors.primaryElement,
              ),
            ),
            Expanded(
              child: RadioListTile<bool>(
                title: const Text("No"),
                value: false,
                groupValue: hasVehicle,
                onChanged: (bool? value) {
                  setState(() {
                    hasVehicle = value ?? false;
                    if (!hasVehicle) {
                      carInfoController.clear();
                    }
                  });
                },
                activeColor: AppColors.primaryElement,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCarInfoField() {
    return TextFormField(
      controller: carInfoController,
      validator: hasVehicle
          ? (value) {
              if (value == null || value.isEmpty) {
                return "Please provide vehicle information";
              }
              return null;
            }
          : null,
      keyboardType: TextInputType.text,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      textInputAction: TextInputAction.done,
      maxLines: 3,
      decoration: const InputDecoration(
        labelText: "Vehicle Information",
        prefixIcon: Icon(Icons.directions_car),
        alignLabelWithHint: true,
        hintText:
            "Please provide details about your vehicle (model, year, etc.)",
      ),
    );
  }

  Future<void> signUpAction() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        isLoading = true;
      });

      try {
        var map = <String, dynamic>{
          "name": nameController.text.trim(),
          "number": mobileController.text.trim(),
          "email": emailController.text.trim(),
          "city": cityController.text.trim(),
          "carTaken": hasVehicle,
          "carInfo": hasVehicle ? carInfoController.text.trim() : "",
        };

        ApiResponse<dynamic> response = await _repository.signUpAction(map);

        if (!mounted) return;

        if (response.status == Status.COMPLETED) {
          _showSuccessDialog(
            response.data["message"] ?? "Thank you for Sign-up",
          );
        } else {
          _showErrorDialog(response.message ?? "An error occurred");
        }
      } catch (e) {
        if (mounted) {
          _showErrorDialog("An error occurred during sign up");
        }
      } finally {
        if (mounted) {
          setState(() {
            isLoading = false;
          });
        }
      }
    }
  }

  void _showSuccessDialog(String message) {
    if (!mounted) return;
    Get.defaultDialog(
      title: "Success",
      middleText: message,
      textConfirm: "OK",
      buttonColor: AppColors.primaryElement,
      confirmTextColor: Colors.white,
      onConfirm: () {
        Get.back(); // Close dialog
        Get.back(); // Go back to login page
      },
    );
  }

  void _showErrorDialog(String message) {
    if (!mounted) return;
    Get.defaultDialog(
      title: "Error",
      middleText: message,
      textConfirm: "OK",
      buttonColor: AppColors.primaryElement,
      confirmTextColor: Colors.white,
      onConfirm: () => Get.back(),
    );
  }
}
