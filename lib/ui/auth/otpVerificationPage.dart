import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../apiService/api_response.dart';
import '../../apiService/authentication_manager.dart';
import '../../apiService/jsonPlacehoderProvider.dart';
import '../../apiService/model/profileDetailsModel.dart';
import '../../globle/Constants.dart';
import '../../globle/myButton.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

class OtpVerificationPage extends StatefulWidget {
  final String? mobileNo;
  final String? timer;
  final bool isSignUp;

  const OtpVerificationPage({
    Key? key,
    this.mobileNo,
    this.timer,
    this.isSignUp = false,
  }) : super(key: key);

  @override
  State<OtpVerificationPage> createState() => _OtpVerificationPageState();
}

class _OtpVerificationPageState extends State<OtpVerificationPage> {
  final TextEditingController _otpController = TextEditingController();
  final AuthenticationManager _authManager = Get.find();
  final JsonPlaceholerProvider _repository = JsonPlaceholerProvider();
  String _otpCode = '';
  bool _enableButton = false;
  bool globalEnable = true;
  final int _otpCodeLength = 6;
  bool _isLoadingButton = false;
  late int timerMaxSeconds;
  int currentSeconds = 0;
  String get timerText =>
      "${((timerMaxSeconds - currentSeconds) ~/ 60).toString().padLeft(2, '0')}:${((timerMaxSeconds - currentSeconds) % 60).toString().padLeft(2, '0')}";
  Timer? timerObj;
  bool isEnableTimer = true;
  final List<TextEditingController> _controllers = List.generate(
    6,
    (_) => TextEditingController(),
  );
  final List<FocusNode> _focusNodes = List.generate(6, (_) => FocusNode());

  @override
  void initState() {
    super.initState();
    // Initialize timer with the value from the API or default to 60 seconds
    timerMaxSeconds = int.tryParse(widget.timer ?? '1') ?? 1;
    timerMaxSeconds *= 60; // Convert minutes to seconds
    startTimer();
  }

  @override
  void dispose() {
    _otpController.dispose();
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var node in _focusNodes) {
      node.dispose();
    }
    timerObj?.cancel();
    super.dispose();
  }

  void startTimer() {
    timerObj = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (currentSeconds < timerMaxSeconds) {
          currentSeconds++;
        } else {
          timer.cancel();
          isEnableTimer = false;
        }
      });
    });
  }

  void _onOtpChanged(String value, int index) {
    if (value.length == 1) {
      if (index < _otpCodeLength - 1) {
        _focusNodes[index + 1].requestFocus();
      } else {
        _focusNodes[index].unfocus();
      }
    }

    String code = _controllers.map((c) => c.text).join();
    setState(() {
      _otpCode = code;
      _enableButton = code.length == _otpCodeLength;
    });
  }

  Widget _otpWidget() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: List.generate(
        _otpCodeLength,
        (index) => SizedBox(
          width: 45,
          child: TextField(
            controller: _controllers[index],
            focusNode: _focusNodes[index],
            keyboardType: TextInputType.number,
            textAlign: TextAlign.center,
            maxLength: 1,
            decoration: InputDecoration(
              counterText: '',
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.primaryElement),
              ),
            ),
            onChanged: (value) => _onOtpChanged(value, index),
          ),
        ),
      ),
    );
  }

  Widget _rowWidget() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "Don't receive the code? ",
          style: TextStyle(color: Colors.grey[600], fontSize: 15),
        ),
        TextButton(
          onPressed: isEnableTimer
              ? null
              : () {
                  setState(() {
                    currentSeconds = 0;
                    isEnableTimer = true;
                    startTimer();
                    sendOtpApiCall(true);
                  });
                },
          child: Text(
            isEnableTimer ? timerText : "Resend",
            style: TextStyle(
              color: isEnableTimer ? Colors.grey : AppColors.primaryElement,
              fontSize: 15,
            ),
          ),
        ),
      ],
    );
  }

  Future<void> sendOtpApiCall(bool inBackground) async {
    try {
      var map = <String, dynamic>{};
      map["username"] = widget.mobileNo;
      ApiResponse<dynamic> model = await _repository.sendOtpAction(map);
      if (model.status == Status.COMPLETED) {
        if (!mounted) return;
        if (model.data['data']['status'] == "true") {
          // Update timer if provided in response
          if (model.data["Remaining"] != null) {
            setState(() {
              timerMaxSeconds = int.parse(model.data["Remaining"]) * 60;
              currentSeconds = 0;
              isEnableTimer = true;
              startTimer();
            });
          }
          if (inBackground) {
            Get.snackbar(
              'Success',
              model.data['message'] ?? '',
              snackPosition: SnackPosition.BOTTOM,
            );
          }
        } else {
          Get.snackbar(
            'Error',
            model.data['message'] ?? '',
            snackPosition: SnackPosition.BOTTOM,
          );
        }
      } else {
        Get.snackbar(
          'Error',
          model.message ?? '',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to send OTP. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('OTP Verification')),
      body: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(height: 50),
            const Text(
              'Enter OTP',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            Text(
              'We have sent a verification code to\n${widget.mobileNo}',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16, color: Colors.grey[600]),
            ),
            const SizedBox(height: 30),
            _otpWidget(),
            const SizedBox(height: 20),
            _rowWidget(),
            const SizedBox(height: 30),
            Center(
              child: (_enableButton && globalEnable)
                  ? MyButton(onTap: submitAction, title: "Submit")
                  : ElevatedButton(
                      onPressed: null,
                      style: ElevatedButton.styleFrom(
                        minimumSize: const Size(double.infinity, 50),
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.all(Radius.circular(5)),
                        ),
                      ),
                      child: const Text(
                        'Submit',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
            ).paddingOnly(bottom: 10, left: 20, right: 20),
          ],
        ),
      ),
    );
  }

  Future<void> submitAction() async {
    if (_otpCode.length == 6) {
      var map = <String, dynamic>{};
      map["username"] = widget.mobileNo;
      map["otp"] = _otpCode;
      map["fcmToken"] = AppConstants.fcmToken;

      debugPrint('Sending FCM token to backend: ${AppConstants.fcmToken}');

      ApiResponse<dynamic> model = await _repository.verifyOtpAction(map);
      if (model.status == Status.COMPLETED) {
        if (!mounted) return;

        String jsonData = jsonEncode(model.data);
        ProfileDetailsModel profileDetailsModel = ProfileDetailsModel.fromJson(
          model.data,
        );
        if (profileDetailsModel.data?.token != null) {
          Navigator.of(context).popUntil((route) => route.isFirst);
          AppConstants.currentProfile = profileDetailsModel.data!;
          _authManager.login(jsonData);

          try {
            final String? currentToken = await FirebaseMessaging.instance
                .getToken();
            if (currentToken != null && currentToken != AppConstants.fcmToken) {
              debugPrint('FCM token changed after login, updating...');
              AppConstants.fcmToken = currentToken;
            }
          } catch (e) {
            debugPrint('Error verifying FCM token after login: $e');
          }
        } else {
          if (model.data['data']['status'] == "false") {
            sendOtpApiCall(true);
          }
          Get.defaultDialog(
            title: "",
            middleText: model.data['message'] ?? '',
            textConfirm: "OK",
            buttonColor: AppColors.primaryElement,
            titleStyle: const TextStyle(fontSize: 0),
            onConfirm: () => Get.back(),
          );
        }
      } else {
        Get.defaultDialog(
          title: "",
          middleText: model.message ?? '',
          textConfirm: "OK",
          buttonColor: AppColors.primaryElement,
          titleStyle: const TextStyle(fontSize: 0),
          onConfirm: () => Get.back(),
        );
      }
    }
  }
}
