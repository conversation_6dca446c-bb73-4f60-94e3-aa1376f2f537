import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kataria/globle/common.dart';
import 'package:kataria/ui/auth/otpVerificationPage.dart';
import 'package:kataria/ui/auth/signupPage.dart';
import '../../apiService/api_response.dart';
// import '../../apiService/authentication_manager.dart';
import '../../apiService/jsonPlacehoderProvider.dart';
import '../../globle/Constants.dart';
import '../../globle/myButton.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  TextEditingController emailController = TextEditingController();
  // TextEditingController passwordController = TextEditingController();
  final JsonPlaceholerProvider _repository = JsonPlaceholerProvider();

  final _formKey = GlobalKey<FormState>();
  bool showSignUpButton = true;

  // late final AuthenticationManager _authManager = Get.put(AuthenticationManager());
  @override
  void initState() {
    super.initState();
    _checkDeviceTypeConfig();
  }

  Future<void> _checkDeviceTypeConfig() async {
    try {
      ApiResponse<dynamic> response = await _repository.getDeviceTypeConfig();
      if (mounted) {
        if (response.status == Status.COMPLETED) {
          final deviceConfig = response.data;
          final bool androidEnabled = deviceConfig['android'] ?? true;
          final bool iosEnabled = deviceConfig['ios'] ?? true;

          setState(() {
            if (Platform.isAndroid) {
              showSignUpButton = androidEnabled;
            } else if (Platform.isIOS) {
              showSignUpButton = iosEnabled;
            }
          });
        } else {
          setState(() {
            showSignUpButton = true;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          showSignUpButton = true;
        });
      }
    }
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    emailController.dispose();
    // passwordController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: SafeArea(
          child: Form(
            key: _formKey,
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                // crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  // SizedBox(height: 0.01.sh,),
                  Image.asset("assets/images/logo.png", height: 200).vp(0),
                  // Permissions(),
                  Text(
                    "Login",
                    style: Theme.of(context).textTheme.headlineLarge,
                  ),
                  Text(
                    "Login to access our services",
                    style: Theme.of(context).textTheme.titleMedium,
                  ).bp(50),
                  _emailWidget().bp(60),
                  // _passwordWidget().bp(60),
                  Center(
                    child: MyButton(onTap: loginAction, title: "Login"),
                  ).bp(10),

                  if (showSignUpButton)
                    Center(
                      child: TextButton(
                        onPressed: () {
                          Get.to(() => const SignUpPage());
                        },
                        child: RichText(
                          text: TextSpan(
                            style: Theme.of(context).textTheme.bodyMedium,
                            children: [
                              const TextSpan(text: "Don't have an account? "),
                              TextSpan(
                                text: "Sign Up",
                                style: TextStyle(
                                  color: AppColors.primaryElement,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ).bp(10),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<dynamic> loginAction() async {
    // await Future.delayed(const Duration(seconds: 3));
    // Get.to(()=> OtpVerificationPage(mobileNo: emailController.text,timer: '1',));
    // return;
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();
      try {
        // await Future.delayed(const Duration(seconds: 1));
        var map = <String, dynamic>{};
        map["username"] = emailController.text;
        ApiResponse<dynamic> model = await _repository.otpSendAction(map);
        if (!mounted) return;

        if (model.status == Status.COMPLETED) {
          Get.to(
            () => OtpVerificationPage(
              mobileNo: emailController.text,
              timer: model.data["Remaining"],
            ),
          );
        } else {
          Get.defaultDialog(
            title: "",
            middleText: model.message ?? '',
            textConfirm: "OK",
            buttonColor: AppColors.primaryElement,
            titleStyle: const TextStyle(fontSize: 0),
            onConfirm: () => Get.back(),
            // textCancel: "Cancel",
          );
          // Get.snackbar('Error', model.message ?? '',snackPosition: SnackPosition.BOTTOM);
        }
        // _authManager.login("isLogin");
      } catch (e) {
        // Get.defaultDialog(
        //     content: const Text('User not found!'),
        //     textConfirm: 'OK',
        //     titleStyle: const TextStyle(fontSize: 0),
        //     middleText: "",
        //     confirmTextColor: Colors.black,
        //     onConfirm: () {
        //       Get.back();
        //     });
      }
    }
  }

  Widget _emailWidget() {
    return TextFormField(
      controller: emailController,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return "Please enter the phone number of the lead.";
        } else if (value.length > 10) {
          return "Phone number should not exceed 10 characters.";
        } else if (value.length < 10) {
          return "Phone number should be exactly 10 characters.";
        }
        return null;
      },
      keyboardType: TextInputType.phone,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      textInputAction: TextInputAction.done, // Hides the keyboard.
      // style: Theme.of(context).textTheme.subtitle1,
      onChanged: (value) {},
      decoration: const InputDecoration(
        isDense: false,
        counterText: '',
        labelText: "Phone Number",
        prefixIcon: Icon(Icons.phone_iphone),
      ),
    );
  }

  // Widget _passwordWidget() {
  //   return TextFormField(
  //       controller: passwordController,
  //       validator: (value) {
  //         if (value == null || value.isEmpty) {
  //           return "Please enter a password.";
  //         } else if (value.length > 16) {
  //           return "Maximum 16 characters allowed";
  //         } else if (value.length < 6) {
  //           return "Password must be have at least 6 characters";
  //         }
  //         return null;
  //       },
  //       obscureText: _isObscure,
  //       autovalidateMode: AutovalidateMode.onUserInteraction,
  //       textInputAction: TextInputAction.done, // Hides the keyboard.
  //       // style: Theme.of(context).textTheme.subtitle1,
  //       onChanged: (value) {},
  //       decoration: InputDecoration(
  //         isDense: false,
  //         errorMaxLines: 2,
  //         prefixIcon: const Icon(Icons.password_outlined),
  //         suffixIcon: IconButton(
  //             icon: Icon(
  //               _isObscure ? Icons.visibility_off : Icons.visibility,
  //               color: Colors.grey,
  //             ),
  //             onPressed: () {
  //               setState(() {
  //                 _isObscure = !_isObscure;
  //               });
  //             }),
  //         labelText: "Password",
  //       ));
  // }

  Future<bool?> _handleClickMe(String content) async {
    var r = await showDialog<bool>(
      context: context,
      barrierDismissible: false, // user must tap button!
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          contentPadding: const EdgeInsets.fromLTRB(24.0, 15.0, 24.0, 15),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text(
                  content,
                  style: const TextStyle(
                    fontFamily: "SFProRegular",
                    fontSize: 18,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      style: TextButton.styleFrom(
                        backgroundColor: Colors.black,
                      ),
                      child: const Text(
                        "Ok",
                        style: TextStyle(color: Colors.white),
                      ),
                      onPressed: () async {
                        Navigator.pop(context, true);
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
    return r;
  }
}
