import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kataria/globle/common.dart';
import '../../apiService/model/leadListModel.dart';
import 'controller/leadStatusController.dart';

class LeadStatusPage extends StatelessWidget {
  LeadStatusPage({super.key});
  LeadStatusController leadStatusController = Get.put(LeadStatusController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Lead Status")),
      body: Obx(() {
        return leadStatusController.isLoading.value
            ? const Center(child: CircularProgressIndicator())
            : leadStatusController.list.isEmpty
            ? const Center(child: Text('No any leads found'))
            : ListView.builder(
                itemCount: leadStatusController.list.length,
                padding: const EdgeInsets.symmetric(vertical: 15),
                itemBuilder: (BuildContext context, int index) {
                  LeadList obj = leadStatusController.list[index];
                  String imageUrl = "";
                  if (obj.logo != '') {
                    imageUrl = obj.logo!;
                    // print(imageUrl);
                  }
                  String color = '0xFF${obj.color}';
                  return Container(
                    margin: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 10,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(color: Color(int.parse(color))),
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            CachedNetworkImage(
                              width: Get.width / 4,
                              imageUrl: imageUrl,
                              fit: BoxFit.fitWidth,
                              placeholder: (context, url) => const Center(
                                child: CircularProgressIndicator(),
                              ),
                              errorWidget: (context, url, error) =>
                                  const Icon(Icons.error),
                            ).paddingAll(10),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: [
                                  Text(
                                    obj.name?.capitalize ?? '',
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w800,
                                      color: Colors.black,
                                    ),
                                  ).bp(10),
                                  Text(
                                    obj.phone ?? '',
                                    style: const TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w200,
                                      color: Colors.blueGrey,
                                    ),
                                  ),
                                  if (obj.email != '')
                                    Text(
                                      obj.email ?? '',
                                      style: const TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w200,
                                        color: Colors.blueGrey,
                                      ),
                                    ),
                                  if (obj.empRemarks != '')
                                    Text(
                                      'Remarks: ${obj.empRemarks}',
                                      style: const TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w200,
                                        color: Colors.black,
                                      ),
                                    ),
                                  Row(
                                    children: [
                                      const Text(
                                        'Status: ',
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w200,
                                          color: Colors.black,
                                        ),
                                      ),

                                      Text(
                                        obj.statusEnum ?? '',
                                        style: TextStyle(
                                          fontSize: 11,
                                          fontWeight: FontWeight.w800,
                                          color: Color(int.parse(color)),
                                        ),
                                      ),
                                    ],
                                  ).bp(10),
                                ],
                              ).paddingAll(20),
                            ),
                          ],
                        ),
                        Container(
                          margin: const EdgeInsets.only(top: 10),
                          padding: const EdgeInsets.all(10),
                          color: Color(int.parse(color)),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                obj.product ?? '',
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w700,
                                  color: Colors.white,
                                ),
                              ),
                              if (obj.salesExecutive != '')
                                RichText(
                                  text: TextSpan(
                                    text: obj.salesExecutive ?? '',
                                    style: const TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.white,
                                    ),
                                    children: [
                                      if (obj.salesExecutiveBranch != '')
                                        TextSpan(
                                          text:
                                              ' (${obj.salesExecutiveBranch})',
                                          style: const TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                            color: Colors.white,
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                              // Text(obj.salesExecutive ?? '',
                              //     style: const TextStyle(
                              //         fontSize: 12,
                              //         fontWeight: FontWeight.w500,
                              //         color: Colors.white)),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              );
      }),
    );
  }
}
