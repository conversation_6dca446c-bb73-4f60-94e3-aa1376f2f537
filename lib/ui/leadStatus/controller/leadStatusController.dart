import 'package:get/get.dart';

import '../../../apiService/api_response.dart';
import '../../../apiService/jsonPlacehoderProvider.dart';
import '../../../apiService/model/leadListModel.dart';

class LeadStatusController extends GetxController {
  final JsonPlaceholerProvider _repository = JsonPlaceholerProvider();
  var list = <LeadList>[].obs;
  RxBool isLoading = false.obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    getLeadList();
  }

  getLeadList() async {
    try {
      isLoading(true);
      ApiResponse<dynamic> model = await _repository.getCreateLeadList();
      if (model.status == Status.COMPLETED) {
        LeadListModel leadListModel = LeadListModel.fromJson(model.data);
        list.value = leadListModel.leadList ?? [];
        // print(leadListModel);
      }
    } catch (e) {
      print('Error while getting data is $e');
    } finally {
      isLoading(false);
    }
  }
}
