// // import "package:flutter/material.dart";
// // import 'package:autoapp/globle/text_style.dart';
// // import 'button_theme.dart';
// // import 'input_decoration_theme.dart';
// //
// // enum MyAppThemeKeys { LIGHT, DAR<PERSON> }
// //
// // class MyAppTheme {
// //   static final ThemeData lightTheme = ThemeData(
// //     appBarTheme: AppBarTheme(
// //       color: Colors.white,
// //       elevation: 0,
// //       iconTheme: const IconThemeData(color: Color(0xFF000000)),
// //       titleTextStyle: AppTextStyles.medium(),
// //       centerTitle: true,
// //     ),
// //     splashColor: Colors.transparent,
// //     highlightColor: Colors.transparent,
// //     hoverColor: Colors.transparent,
// //     fontFamily: "Inter",
// //     primaryColor: Colors.black,
// //     scaffoldBackgroundColor: Colors.white,
// //     canvasColor: Colors.white,
// //     brightness: Brightness.light,
// //     colorScheme: ColorScheme.fromSwatch().copyWith(
// //       secondary: Colors.white, // Your accent color
// //     ),
// //     unselectedWidgetColor: Colors.blue,
// //     elevatedButtonTheme: elevatedButtonThemeLight(),
// //     inputDecorationTheme: inputDecorationThemeLight(),
// //     // textButtonTheme: textButtonThemeLight(),
// //     // textTheme: textThemeLight(),
// //     // inputDecorationTheme: inputDecorationThemeLight(),
// //     // textSelectionTheme: const TextSelectionThemeData(
// //     //   cursorColor: AppColors.primaryElement,
// //     // )
// //   );
// //
// //   static ThemeData getThemeFromKey(MyAppThemeKeys themeKey) {
// //     switch (themeKey) {
// //       case MyAppThemeKeys.LIGHT:
// //         return lightTheme;
// //       default:
// //         return lightTheme;
// //     }
// //   }
// // }
//
// import 'package:flutter/material.dart';
//
// import '../globle/Constants.dart';
//
// ThemeData lightTheme = ThemeData(
//     useMaterial3: true,
//     colorScheme: const ColorScheme.light(),
//     primaryColor: const Color(0xff658af7),
//     brightness: Brightness.light,
//     visualDensity: VisualDensity.adaptivePlatformDensity,
//     primarySwatch: Colors.blue,
//     scaffoldBackgroundColor: Colors.grey.shade900,
//     appBarTheme: AppBarTheme(
//         titleTextStyle:
//             const TextStyle(color: Colors.black, fontWeight: FontWeight.w600),
//         iconTheme: const IconThemeData(color: Colors.black),
//         backgroundColor: Colors.grey.shade50,
//         elevation: 0),
//     cardColor: Colors.grey.shade200,
//     progressIndicatorTheme:
//         const ProgressIndicatorThemeData(color: Colors.black),
//     inputDecorationTheme: const InputDecorationTheme(
//       // labelStyle: TextStyle(color: Colors.red),
//       isDense: true,
//       contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
//       hintStyle: TextStyle(color: Color(0xFFDADADA), fontSize: 16.0),
//       enabledBorder: OutlineInputBorder(
//         borderRadius: BorderRadius.all(Radius.circular(8.0)),
//         borderSide: BorderSide(color: Color(0xFFDADADA), width: 1.5),
//       ),
//       focusedBorder: OutlineInputBorder(
//         borderRadius: BorderRadius.all(Radius.circular(8.0)),
//         borderSide: BorderSide(color: Color(0xFFF15A29), width: 2.0),
//       ),
//       focusedErrorBorder: OutlineInputBorder(
//         borderRadius: BorderRadius.all(Radius.circular(8.0)),
//         borderSide: BorderSide(color: Colors.red, width: 2.0),
//       ),
//       errorBorder: OutlineInputBorder(
//         borderRadius: BorderRadius.all(Radius.circular(8.0)),
//         borderSide: BorderSide(color: Colors.red, width: 1.0),
//       ),
//       errorStyle: TextStyle(color: Colors.red),
//     ),
//     elevatedButtonTheme: ElevatedButtonThemeData(
//         style: ElevatedButton.styleFrom(
//       foregroundColor: AppColors.titleText,
//       backgroundColor: AppColors.primaryElement,
//       elevation: 0,
//       minimumSize: const Size(180, 40),
//       textStyle: const TextStyle(
//         fontFamily: "SFProRegular",
//         fontSize: 26,
//       ),
//       padding: const EdgeInsets.symmetric(horizontal: 16),
//       shape: const RoundedRectangleBorder(
//         borderRadius: BorderRadius.all(Radius.circular(10)),
//       ),
//     )),
//     textTheme: TextTheme(
//         displayLarge: const TextStyle(
//             letterSpacing: -1.5,
//             fontSize: 48,
//             color: Colors.black,
//             fontWeight: FontWeight.bold),
//         displayMedium: const TextStyle(
//             letterSpacing: -1.0,
//             fontSize: 40,
//             color: Colors.black,
//             fontWeight: FontWeight.bold),
//         displaySmall: const TextStyle(
//             fontSize: 32, color: Colors.black, fontWeight: FontWeight.bold),
//         headlineMedium: const TextStyle(
//             letterSpacing: -1.0,
//             color: Colors.black,
//             fontSize: 28,
//             fontWeight: FontWeight.w600),
//         headlineSmall: const TextStyle(
//             letterSpacing: -1.0,
//             color: Colors.black,
//             fontSize: 24,
//             fontWeight: FontWeight.w500),
//         titleLarge: const TextStyle(
//             color: Colors.black, fontSize: 24, fontWeight: FontWeight.w600),
//         titleMedium: const TextStyle(
//             color: Colors.black, fontSize: 17, fontWeight: FontWeight.w500),
//         titleSmall: const TextStyle(
//             color: Colors.black, fontSize: 14, fontWeight: FontWeight.w500),
//         bodyLarge: TextStyle(
//             color: Colors.grey.shade700,
//             fontSize: 16,
//             fontWeight: FontWeight.w400),
//         bodyMedium: TextStyle(
//             color: Colors.grey.shade600,
//             fontSize: 14,
//             fontWeight: FontWeight.w400),
//         labelLarge: const TextStyle(
//             color: Colors.white, fontSize: 14, fontWeight: FontWeight.w600),
//         bodySmall: TextStyle(
//             color: Colors.grey.shade800,
//             fontSize: 12,
//             fontWeight: FontWeight.w400),
//         labelSmall: TextStyle(
//             color: Colors.grey.shade700,
//             fontSize: 10,
//             fontWeight: FontWeight.w400,
//             letterSpacing: -0.5)));
//
// ThemeData darkTheme = ThemeData(
//   useMaterial3: true,
//   primaryColor: const Color(0xff658af7),
//   colorScheme: const ColorScheme.dark(),
//   brightness: Brightness.dark,
//   visualDensity: VisualDensity.adaptivePlatformDensity,
//   primarySwatch: Colors.blue,
//   scaffoldBackgroundColor: Colors.grey.shade900,
//   appBarTheme: AppBarTheme(
//     backgroundColor: Colors.grey.shade900,
//     elevation: 0,
//     iconTheme: const IconThemeData(color: Colors.white),
//   ),
//   cardColor: Colors.grey.shade200,
//   progressIndicatorTheme:
//       const ProgressIndicatorThemeData(color: Color(0xFFECB141)),
//   inputDecorationTheme: const InputDecorationTheme(
//     // labelStyle: TextStyle(color: Colors.red),
//     isDense: true,
//     contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
//     hintStyle: TextStyle(color: Color(0xFFDADADA), fontSize: 16.0),
//     enabledBorder: OutlineInputBorder(
//       borderRadius: BorderRadius.all(Radius.circular(8.0)),
//       borderSide: BorderSide(color: Color(0xFFDADADA), width: 1.5),
//     ),
//     focusedBorder: OutlineInputBorder(
//       borderRadius: BorderRadius.all(Radius.circular(8.0)),
//       borderSide: BorderSide(color: Color(0xFFDADADA), width: 2.0),
//     ),
//     focusedErrorBorder: OutlineInputBorder(
//       borderRadius: BorderRadius.all(Radius.circular(8.0)),
//       borderSide: BorderSide(color: Colors.red, width: 2.0),
//     ),
//     errorBorder: OutlineInputBorder(
//       borderRadius: BorderRadius.all(Radius.circular(8.0)),
//       borderSide: BorderSide(color: Colors.red, width: 1.0),
//     ),
//     errorStyle: TextStyle(color: Colors.red),
//   ),
//   elevatedButtonTheme: ElevatedButtonThemeData(
//     style: ElevatedButton.styleFrom(
//       foregroundColor: AppColors.titleText,
//       backgroundColor: AppColors.primaryElement,
//       disabledForegroundColor: AppColors.primaryElement,
//       disabledBackgroundColor: AppColors.primaryElement,
//       shadowColor: AppColors.primaryElement,
//       surfaceTintColor: AppColors.primaryElement,
//       disabledIconColor: AppColors.primaryElement,
//       overlayColor: AppColors.primaryElement,
//       elevation: 0,
//       minimumSize: const Size(180, 40),
//       textStyle: const TextStyle(
//         fontFamily: "SFProRegular",
//         fontSize: 26,
//       ),
//       padding: const EdgeInsets.symmetric(horizontal: 16),
//       shape: const RoundedRectangleBorder(
//         borderRadius: BorderRadius.all(Radius.circular(10)),
//       ),
//     ),
//   ),
//   textTheme: const TextTheme(
//       displayLarge: TextStyle(
//           letterSpacing: -1.5,
//           fontSize: 8,
//           color: Colors.white,
//           fontWeight: FontWeight.bold),
//       displayMedium: TextStyle(
//           letterSpacing: -1.0,
//           fontSize: 40,
//           color: Colors.white,
//           fontWeight: FontWeight.bold),
//       displaySmall: TextStyle(
//           letterSpacing: -1.0,
//           fontSize: 32,
//           color: Colors.white,
//           fontWeight: FontWeight.bold),
//       headlineMedium: TextStyle(
//           letterSpacing: -1.0,
//           color: Colors.white,
//           fontSize: 28,
//           fontWeight: FontWeight.w600),
//       headlineSmall: TextStyle(
//           letterSpacing: -1.0,
//           color: Colors.white,
//           fontSize: 24,
//           fontWeight: FontWeight.w500),
//       titleLarge: TextStyle(
//           color: Colors.white, fontSize: 24, fontWeight: FontWeight.w500),
//       titleMedium: TextStyle(
//           color: Colors.white, fontSize: 17, fontWeight: FontWeight.w500),
//       titleSmall: TextStyle(
//           color: Colors.white, fontSize: 14, fontWeight: FontWeight.w500),
//       bodyLarge: TextStyle(
//           color: Colors.white, fontSize: 16, fontWeight: FontWeight.w400),
//       bodyMedium: TextStyle(
//           color: Colors.white, fontSize: 14, fontWeight: FontWeight.w400),
//       labelLarge: TextStyle(
//           color: Colors.white, fontSize: 14, fontWeight: FontWeight.w600),
//       bodySmall: TextStyle(
//           color: Colors.white, fontSize: 12, fontWeight: FontWeight.w500),
//       labelSmall: TextStyle(
//           color: Colors.white, fontSize: 10, fontWeight: FontWeight.w400)),
// );
import 'package:flutter/material.dart';
import '../globle/Constants.dart';

ThemeData lightmode = ThemeData(
  appBarTheme: AppBarTheme(
    backgroundColor: Colors.grey.shade100,
    iconTheme: const IconThemeData(color: Color(0xFF3C3C3C)),
    titleTextStyle: const TextStyle(fontSize: 20, color: Colors.black),
    foregroundColor: Colors.grey.shade700,
    centerTitle: true,
  ),
  scaffoldBackgroundColor: Colors.white,
  brightness: Brightness.light,
  colorScheme: ColorScheme.light(
    surface: Colors.grey.shade300,
    primary: Colors.grey,
    secondary: Colors.white,
    inversePrimary: Colors.grey.shade700,
  ),
  // cardTheme: const CardTheme(),
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      foregroundColor: AppColors.titleText,
      backgroundColor: AppColors.primaryElement,
      elevation: 0,
      minimumSize: const Size(180, 40),
      textStyle: const TextStyle(fontSize: 20),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(20)),
      ),
    ),
  ),
  textButtonTheme: TextButtonThemeData(
    style: TextButton.styleFrom(
      foregroundColor: AppColors.blackText,
      textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(10)),
      ),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
    ),
  ),
  inputDecorationTheme: const InputDecorationTheme(
    border: OutlineInputBorder(
      borderRadius: BorderRadius.all(Radius.circular(12.0)),
    ),
  ),
  switchTheme: SwitchThemeData(
    thumbColor: WidgetStateProperty.all(AppColors.primaryElement),
    trackOutlineColor: WidgetStateProperty.all(Colors.black),
  ),
);

ThemeData darkmode = ThemeData(
  appBarTheme: AppBarTheme(
    backgroundColor: Colors.black,
    iconTheme: const IconThemeData(color: Color(0xFFFFFFFF)),
    titleTextStyle: const TextStyle(fontSize: 20, color: Colors.white),
    centerTitle: true,
    foregroundColor: Colors.grey.shade300,
  ),
  scaffoldBackgroundColor: Colors.grey.shade900,
  brightness: Brightness.dark,
  colorScheme: ColorScheme.dark(
    surface: Colors.grey.shade900,
    primary: Colors.grey.shade800,
    secondary: Colors.black38,
    inversePrimary: Colors.grey.shade300,
  ),
  // cardTheme: const CardTheme(color: Colors.black38),
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      foregroundColor: AppColors.titleText,
      backgroundColor: AppColors.primaryElement,
      elevation: 0,
      minimumSize: const Size(100, 50),
      textStyle: const TextStyle(fontSize: 14),
      // padding: const EdgeInsets.symmetric(horizontal: 16),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(20)),
      ),
    ),
  ),
  textButtonTheme: TextButtonThemeData(
    style: TextButton.styleFrom(
      foregroundColor: AppColors.primaryElement,
      textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(10)),
      ),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
    ),
  ),
  inputDecorationTheme: const InputDecorationTheme(
    border: OutlineInputBorder(
      borderRadius: BorderRadius.all(Radius.circular(12.0)),
    ),
  ),
  switchTheme: SwitchThemeData(
    thumbColor: WidgetStateProperty.all(AppColors.primaryElement),
    trackOutlineColor: WidgetStateProperty.all(Colors.white),
  ),
);
