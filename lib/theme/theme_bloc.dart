// ignore_for_file: depend_on_referenced_packages

import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';

part 'theme_event.dart';

// class ThemeBloc extends Bloc<ThemeEvent, ThemeMode> {
//   ThemeBloc() : super(ThemeMode.system) {
//     on<ChangeThemeEvent>((event, emit) {
//       emit(event.isDark ? ThemeMode.dark : ThemeMode.light);
//     });
//   }
// }
class ThemeBloc extends Bloc<ThemeEvent, ThemeMode> {
  final GetStorage getStorage;
  // final prefs = GetStorage();
  ThemeBloc(super.initialTheme, this.getStorage) {
    on<InitialThemeEvent>((event, emit) async {

      String? savedTheme = getStorage.read('theme');
      ThemeMode initialThemeToLoad =
      savedTheme == 'dark' ? ThemeMode.dark : ThemeMode.light;
      emit(initialThemeToLoad);
    });
    on<ToggleThemeEvent>((event, emit) async {
      //On every toggle theme event, we also have to save the current theme to shared_preferences.So,
      ThemeMode changedTheme =
      state == ThemeMode.dark ? ThemeMode.light : ThemeMode.dark;

      getStorage.write(
          'theme', changedTheme == ThemeMode.dark ? 'dark' : 'light');

      emit(changedTheme);
    });
  }
}