// To parse this JSON data, do
//
//     final profileDetailsModel = profileDetailsModelFromJson(jsonString);

import 'dart:convert';

ProfileDetailsModel profileDetailsModelFromJson(String str) => ProfileDetailsModel.fromJson(json.decode(str));

String profileDetailsModelToJson(ProfileDetailsModel data) => json.encode(data.toJson());

class ProfileDetailsModel {
  String? message;
  ProfileDetails? data;
  dynamic metaData;

  ProfileDetailsModel({
    this.message,
    this.data,
    this.metaData,
  });

  ProfileDetailsModel copyWith({
    String? message,
    ProfileDetails? data,
    dynamic metaData,
  }) =>
      ProfileDetailsModel(
        message: message ?? this.message,
        data: data ?? this.data,
        metaData: metaData ?? this.metaData,
      );

  factory ProfileDetailsModel.fromJson(Map<String, dynamic> json) => ProfileDetailsModel(
    message: json["message"],
    data: json["data"] == null ? null : ProfileDetails.fromJson(json["data"]),
    metaData: json["metaData"],
  );

  Map<String, dynamic> toJson() => {
    "message": message,
    "data": data?.toJson(),
    "metaData": metaData,
  };
}

class ProfileDetails {
  String? token;
  String? username;
  String? empCode;
  String? city;
  String? branch;

  ProfileDetails({
    this.token,
    this.username,
    this.empCode,
    this.branch,
    this.city
  });

  ProfileDetails copyWith({
    String? token,
    String? username,
    String? empCode,
    String? city,
    String? branch,
  }) =>
      ProfileDetails(
        token: token ?? this.token,
        username: username ?? this.username,
        empCode: username ?? this.empCode,
        city: city ?? this.city,
        branch: branch ?? this.branch,
      );

  factory ProfileDetails.fromJson(Map<String, dynamic> json) => ProfileDetails(
    token: json["token"],
    username: json["username"],
    empCode: json["empCode"],
    branch: json["branch"],
    city: json["city"],
  );

  Map<String, dynamic> toJson() => {
    "token": token,
    "username": username,
    "empCode": empCode,
    "city": city,
    "branch": branch,
  };
}
