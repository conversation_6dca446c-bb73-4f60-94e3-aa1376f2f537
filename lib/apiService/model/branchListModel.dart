// To parse this JSON data, do
//
//     final branchListModel = branchListModelFromJson(jsonString);

import 'dart:convert';

BranchListModel branchListModelFromJson(String str) => BranchListModel.fromJson(json.decode(str));

String branchListModelToJson(BranchListModel data) => json.encode(data.toJson());

class BranchListModel {
  String? message;
  List<BranchList>? data;
  MetaData? metaData;

  BranchListModel({
    this.message,
    this.data,
    this.metaData,
  });

  BranchListModel copyWith({
    String? message,
    List<BranchList>? data,
    MetaData? metaData,
  }) =>
      BranchListModel(
        message: message ?? this.message,
        data: data ?? this.data,
        metaData: metaData ?? this.metaData,
      );

  factory BranchListModel.fromJson(Map<String, dynamic> json) => BranchListModel(
    message: json["message"],
    data: json["data"] == null ? [] : List<BranchList>.from(json["data"]!.map((x) => BranchList.fromJson(x))),
    metaData: json["metaData"] == null ? null : MetaData.fromJson(json["metaData"]),
  );

  Map<String, dynamic> toJson() => {
    "message": message,
    "data": data == null ? [] : List<dynamic>.from(data!.map((x) => x.toJson())),
    "metaData": metaData?.toJson(),
  };
}

class BranchList {
  String? id;
  String? name;
  String? cityName;
  String? status;

  BranchList({
    this.id,
    this.name,
    this.cityName,
    this.status,
  });

  BranchList copyWith({
    String? id,
    String? name,
    String? cityName,
    String? status,
  }) =>
      BranchList(
        id: id ?? this.id,
        name: name ?? this.name,
        cityName: cityName ?? this.cityName,
        status: status ?? this.status,
      );

  factory BranchList.fromJson(Map<String, dynamic> json) => BranchList(
    id: json["id"],
    name: json["name"],
    cityName: json["cityName"],
    status: json["status"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "cityName": cityName,
    "status": status,
  };
}

class MetaData {
  int? pageSize;
  int? currentPage;
  int? totalElements;
  int? totalPages;

  MetaData({
    this.pageSize,
    this.currentPage,
    this.totalElements,
    this.totalPages,
  });

  MetaData copyWith({
    int? pageSize,
    int? currentPage,
    int? totalElements,
    int? totalPages,
  }) =>
      MetaData(
        pageSize: pageSize ?? this.pageSize,
        currentPage: currentPage ?? this.currentPage,
        totalElements: totalElements ?? this.totalElements,
        totalPages: totalPages ?? this.totalPages,
      );

  factory MetaData.fromJson(Map<String, dynamic> json) => MetaData(
    pageSize: json["pageSize"],
    currentPage: json["currentPage"],
    totalElements: json["totalElements"],
    totalPages: json["totalPages"],
  );

  Map<String, dynamic> toJson() => {
    "pageSize": pageSize,
    "currentPage": currentPage,
    "totalElements": totalElements,
    "totalPages": totalPages,
  };
}
