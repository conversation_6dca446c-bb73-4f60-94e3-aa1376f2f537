class LeadListModel {
  String? message;
  List<LeadList>? leadList;

  LeadListModel({this.message, this.leadList});

  LeadListModel.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    if (json['data'] != null) {
      leadList = <LeadList>[];
      json['data'].forEach((v) {
        leadList!.add(LeadList.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data =  <String, dynamic>{};
    data['message'] = message;
    if (leadList != null) {
      data['data'] = leadList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class LeadList {
  String? id;
  String? statusEnum;
  String? color;
  String? status;
  String? product;
  String? company;
  String? subCompany;
  String? logo;
  String? name;
  String? phone;
  String? email;
  String? remarks;
  String? empRemarks;
  String? dmsNumber;
  String? empName;
  String? empBranch;
  String? empDepartment;
  String? salesExecutive;
  String? salesExecutiveBranch;

  LeadList(
      {this.id,
        this.statusEnum,
        this.color,
        this.status,
        this.product,
        this.company,
        this.subCompany,
        this.logo,
        this.name,
        this.phone,
        this.email,
        this.remarks,
        this.empRemarks,
        this.dmsNumber,
        this.empName,
        this.empBranch,
        this.empDepartment,
        this.salesExecutiveBranch,
        this.salesExecutive});

  LeadList.fromJson(Map<String, dynamic> json) {
    id = json['id'] ?? '';
    statusEnum = json['statusEnum'] ?? '';
    color = json['color'] ?? '';
    status = json['status'] ?? '';
    product = json['product'] ?? '';
    company = json['company'] ?? '';
    subCompany = json['subCompany'] ?? '';
    logo = json['logo'] ?? '';
    name = json['name'] ?? '';
    phone = json['phone'] ?? '';
    email = json['email'] ?? '';
    remarks = json['remarks'] ?? '';
    empRemarks = json['empRemarks'] ?? '';
    dmsNumber = json['dmsNumber'] ?? '';
    empName = json['empName'] ?? '';
    empBranch = json['empBranch'] ?? '';
    empDepartment = json['empDepartment'] ?? '';
    salesExecutive = json['salesExecutive'] ?? '';
    salesExecutiveBranch = json['salesExecutiveBranch'] ?? '';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['statusEnum'] = statusEnum;
    data['color'] = color;
    data['status'] = status;
    data['product'] = product;
    data['company'] = company;
    data['subCompany'] = subCompany;
    data['logo'] = logo;
    data['name'] = name;
    data['phone'] = phone;
    data['email'] = email;
    data['remarks'] = remarks;
    data['empRemarks'] = empRemarks;
    data['dmsNumber'] = dmsNumber;
    data['empName'] = empName;
    data['empBranch'] = empBranch;
    data['empDepartment'] = empDepartment;
    data['salesExecutive'] = salesExecutive;
    data['salesExecutiveBranch'] = salesExecutiveBranch;
    return data;
  }
}
