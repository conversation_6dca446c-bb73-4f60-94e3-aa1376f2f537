// To parse this JSON data, do
//
//     final announcementListModel = announcementListModelFromJson(jsonString);

import 'dart:convert';

AnnouncementListModel announcementListModelFromJson(String str) =>
    AnnouncementListModel.fromJson(json.decode(str));

String announcementListModelToJson(AnnouncementListModel data) =>
    json.encode(data.toJson());

class AnnouncementListModel {
  final String? message;
  final List<AnnouncementList>? data;
  final MetaData? metaData;

  AnnouncementListModel({
    this.message,
    this.data,
    this.metaData,
  });

  factory AnnouncementListModel.fromJson(Map<String, dynamic> json) =>
      AnnouncementListModel(
        message: json["message"],
        data: json["data"] == null
            ? []
            : List<AnnouncementList>.from(
                json["data"]!.map((x) => AnnouncementList.fromJson(x))),
        metaData: json["metaData"] == null
            ? null
            : MetaData.fromJson(json["metaData"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "metaData": metaData?.toJson(),
      };
}

class AnnouncementList {
  final String? id;
  final String? title;
  final String? description;
  final String? type;
  final bool? draft;
  final DateTime? updatedAt;
  AnnouncementList(
      {this.id,
      this.title,
      this.description,
      this.type,
      this.draft,
      this.updatedAt});

  factory AnnouncementList.fromJson(Map<String, dynamic> json) =>
      AnnouncementList(
        id: json["id"],
        title: json["title"],
        description: json["description"],
        type: json["type"],
        draft: json["draft"],
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "description": description,
        "type": type,
        "draft": draft,
        "updatedAt": updatedAt?.toIso8601String(),
      };
}

class MetaData {
  final int? pageSize;
  final int? currentPage;
  final int? totalElements;
  final int? totalPages;

  MetaData({
    this.pageSize,
    this.currentPage,
    this.totalElements,
    this.totalPages,
  });

  factory MetaData.fromJson(Map<String, dynamic> json) => MetaData(
        pageSize: json["pageSize"],
        currentPage: json["currentPage"],
        totalElements: json["totalElements"],
        totalPages: json["totalPages"],
      );

  Map<String, dynamic> toJson() => {
        "pageSize": pageSize,
        "currentPage": currentPage,
        "totalElements": totalElements,
        "totalPages": totalPages,
      };
}
