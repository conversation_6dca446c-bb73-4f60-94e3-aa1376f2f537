// To parse this JSON data, do
//
//     final companyListModel = companyListModelFromJson(jsonString);

import 'dart:convert';

CompanyListModel companyListModelFromJson(String str) => CompanyListModel.fromJson(json.decode(str));

String companyListModelToJson(CompanyListModel data) => json.encode(data.toJson());

class CompanyListModel {
  final String message;
  final List<CompanyList> data;
  final dynamic metaData;

  CompanyListModel({
    required this.message,
    required this.data,
    required this.metaData,
  });

  factory CompanyListModel.fromJson(Map<String, dynamic> json) => CompanyListModel(
    message: json["message"],
    data: List<CompanyList>.from(json["data"].map((x) => CompanyList.fromJson(x))),
    metaData: json["metaData"],
  );

  Map<String, dynamic> toJson() => {
    "message": message,
    "data": List<dynamic>.from(data.map((x) => x.toJson())),
    "metaData": metaData,
  };
}

class CompanyList {
  final String id;
  final String name;
  final String originalDocName;
  final String docData;
  final String logo;
  final List<SubCompanyList> subCompanies;

  CompanyList({
    required this.id,
    required this.name,
    required this.originalDocName,
    required this.docData,
    required this.logo,
    required this.subCompanies,
  });

  factory CompanyList.fromJson(Map<String, dynamic> json) => CompanyList(
    id: json["id"] ?? '',
    name: json["name"] ?? '',
    subCompanies: List<SubCompanyList>.from(json["subCompanies"].map((x) => SubCompanyList.fromJson(x))),
    originalDocName: json["originalDocName"] ?? '',
    docData: json["docData"] ?? '',
    logo: json["logo"] ?? '',
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "originalDocName": originalDocName,
    "docData": docData,
    "logo": logo,
    "subCompanies": List<dynamic>.from(subCompanies.map((x) => x.toJson())),
  };
}

class SubCompanyList {
  final String id;
  final String name;
  final String originalDocName;
  final String docData;
  final String logo;

  SubCompanyList({
    required this.id,
    required this.name,
    required this.originalDocName,
    required this.docData,
    required this.logo,
  });

  factory SubCompanyList.fromJson(Map<String, dynamic> json) => SubCompanyList(
    id: json["id"] ?? '',
    name: json["name"] ?? '',
    originalDocName: json["originalDocName"] ?? '',
    docData: json["docData"] ?? '',
    logo: json["logo"] ?? '',
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "originalDocName": originalDocName,
    "docData": docData,
    "logo": logo,
  };
}
