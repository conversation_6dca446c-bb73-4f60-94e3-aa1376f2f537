// To parse this JSON data, do
//
//     final productListModel = productListModelFromJson(jsonString);

import 'dart:convert';

ProductListModel productListModelFromJson(String str) => ProductListModel.fromJson(json.decode(str));

String productListModelToJson(ProductListModel data) => json.encode(data.toJson());

class ProductListModel {
  final String? message;
  final List<ProductList>? data;
  final dynamic metaData;

  ProductListModel({
    this.message,
    this.data,
    this.metaData,
  });

  factory ProductListModel.fromJson(Map<String, dynamic> json) => ProductListModel(
    message: json["message"],
    data: json["data"] == null ? [] : List<ProductList>.from(json["data"]!.map((x) => ProductList.fromJson(x))),
    metaData: json["metaData"],
  );

  Map<String, dynamic> toJson() => {
    "message": message,
    "data": data == null ? [] : List<dynamic>.from(data!.map((x) => x.toJson())),
    "metaData": metaData,
  };
}

class ProductList {
  final String? id;
  final String? name;

  ProductList({
    this.id,
    this.name,
  });

  factory ProductList.fromJson(Map<String, dynamic> json) => ProductList(
    id: json["id"],
    name: json["name"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
  };
}
