import "dart:io";
import "package:dio/dio.dart";
import '../globle/Constants.dart';
import 'api_response.dart';

typedef OnUploadProgressCallback = void Function(int sentBytes, int totalBytes);

class JsonPlaceholerProvider {
  // var baseUrl = AppConstants.baseUrl;

  Dio dio = Dio();

  // Future<ApiResponse<dynamic>> btnUpdateImage(FormData map, OnUploadProgressCallback? onUploadProgress) async {
  //   try {
  //     Response response = await dio.post(
  //       AppConstants.baseUrl + ApiConstants.uploadMultipleImages,
  //       data: map,
  //     );
  //     return ApiResponse.completed(response.data);
  //   } on DioException catch (e) {
  //     if (e.type == DioExceptionType.receiveTimeout) {
  //       return ApiResponse.error(AppConstants.noInternet);
  //     } else {
  //       return ApiResponse.error(e.message);
  //     }
  //   }
  // }
  Future<ApiResponse<dynamic>> logoutAction(Map<String, dynamic> map) async {
    dio.options.headers["authorization"] =
        "Bearer ${AppConstants.currentProfile.token ?? " "}";
    try {
      Response response = await dio.post(
        AppConstants.baseUrl + ApiConstants.logout,
        data: map,
      );
      return ApiResponse.completed(response.data);
    } on DioException catch (e) {
      if (e.type == DioExceptionType.receiveTimeout) {
        return ApiResponse.error(AppConstants.noInternet);
      } else if (e.response != null) {
        return ApiResponse.error(e.response?.data['message']);
      } else {
        return ApiResponse.error((e.error as SocketException).message);
      }
    }
  }

  Future<ApiResponse<dynamic>> otpSendAction(Map<String, dynamic> map) async {
    try {
      Response response = await dio.post(
        AppConstants.baseUrl + ApiConstants.otpSend,
        data: map,
      );
      return ApiResponse.completed(response.data);
    } on DioException catch (e) {
      if (e.type == DioExceptionType.receiveTimeout) {
        return ApiResponse.error(AppConstants.noInternet);
      } else if (e.response != null) {
        return ApiResponse.error(e.response?.data['message']);
      } else {
        return ApiResponse.error((e.error as SocketException).message);
      }
    }
  }

  Future<ApiResponse<dynamic>> signUpAction(Map<String, dynamic> map) async {
    try {
      Response response = await dio.post(
        AppConstants.baseUrl + ApiConstants.signUp,
        data: map,
      );
      return ApiResponse.completed(response.data);
    } on DioException catch (e) {
      if (e.type == DioExceptionType.receiveTimeout) {
        return ApiResponse.error(AppConstants.noInternet);
      } else if (e.response != null) {
        return ApiResponse.error(e.response?.data['message']);
      } else {
        return ApiResponse.error((e.error as SocketException).message);
      }
    }
  }

  Future<ApiResponse<dynamic>> getDeviceTypeConfig() async {
    try {
      Response response = await dio.get(
        AppConstants.baseUrl + ApiConstants.deviceType,
      );
      return ApiResponse.completed(response.data);
    } on DioException catch (e) {
      if (e.type == DioExceptionType.receiveTimeout) {
        return ApiResponse.error(AppConstants.noInternet);
      } else if (e.response != null) {
        return ApiResponse.error(e.response?.data['message']);
      } else {
        return ApiResponse.error((e.error as SocketException).message);
      }
    }
  }

  Future<ApiResponse<dynamic>> verifyOtpAction(Map<String, dynamic> map) async {
    try {
      Response response = await dio.post(
        AppConstants.baseUrl + ApiConstants.verifyOtp,
        data: map,
      );
      return ApiResponse.completed(response.data);
    } on DioException catch (e) {
      if (e.type == DioExceptionType.receiveTimeout) {
        return ApiResponse.error(AppConstants.noInternet);
      } else if (e.response != null) {
        return ApiResponse.error(e.response?.data['message']);
      } else {
        return ApiResponse.error((e.error as SocketException).message);
      }
    }
  }

  Future<ApiResponse<dynamic>> getCompanyList() async {
    dio.options.headers["authorization"] =
        "Bearer ${AppConstants.currentProfile.token ?? " "}";
    try {
      Response response = await dio.get(
        AppConstants.baseUrl + ApiConstants.company,
      );
      return ApiResponse.completed(response.data);
    } on DioException catch (e) {
      if (e.type == DioExceptionType.receiveTimeout) {
        return ApiResponse.error(AppConstants.noInternet);
      } else if (e.response != null) {
        return ApiResponse.error(e.response?.data['message']);
      } else {
        return ApiResponse.error((e.error as SocketException).message);
      }
    }
  }

  Future<ApiResponse<dynamic>> getProductList(String id) async {
    dio.options.headers["authorization"] =
        "Bearer ${AppConstants.currentProfile.token ?? " "}";
    try {
      Response response = await dio.get(
        AppConstants.baseUrl + ApiConstants.products + id,
      );
      return ApiResponse.completed(response.data);
    } on DioException catch (e) {
      if (e.type == DioExceptionType.receiveTimeout) {
        return ApiResponse.error(AppConstants.noInternet);
      } else if (e.response != null) {
        return ApiResponse.error(e.response?.data['message']);
      } else {
        return ApiResponse.error((e.error as SocketException).message);
      }
    }
  }

  Future<ApiResponse<dynamic>> getBranchList() async {
    dio.options.headers["authorization"] =
        "Bearer ${AppConstants.currentProfile.token ?? " "}";
    try {
      Response response = await dio.get(
        AppConstants.baseUrl + ApiConstants.branches,
      );
      return ApiResponse.completed(response.data);
    } on DioException catch (e) {
      if (e.type == DioExceptionType.receiveTimeout) {
        return ApiResponse.error(AppConstants.noInternet);
      } else if (e.response != null) {
        return ApiResponse.error(e.response?.data['message']);
      } else {
        return ApiResponse.error((e.error as SocketException).message);
      }
    }
  }

  Future<ApiResponse<dynamic>> getAnnouncementList() async {
    dio.options.headers["authorization"] =
        "Bearer ${AppConstants.currentProfile.token ?? " "}";
    try {
      Response response = await dio.get(
        AppConstants.baseUrl + ApiConstants.announcements,
      );
      return ApiResponse.completed(response.data);
    } on DioException catch (e) {
      if (e.type == DioExceptionType.receiveTimeout) {
        return ApiResponse.error(AppConstants.noInternet);
      } else if (e.response != null) {
        return ApiResponse.error(e.response?.data['message']);
      } else {
        return ApiResponse.error((e.error as SocketException).message);
      }
    }
  }

  Future<ApiResponse<dynamic>> getNotificationList() async {
    dio.options.headers["authorization"] =
        "Bearer ${AppConstants.currentProfile.token ?? " "}";
    try {
      Response response = await dio.get(
        AppConstants.baseUrl + ApiConstants.getAllNotification,
      );
      return ApiResponse.completed(response.data);
    } on DioException catch (e) {
      if (e.type == DioExceptionType.receiveTimeout) {
        return ApiResponse.error(AppConstants.noInternet);
      } else if (e.response != null) {
        return ApiResponse.error(e.response?.data['message']);
      } else {
        return ApiResponse.error((e.error as SocketException).message);
      }
    }
  }

  Future<ApiResponse<dynamic>> submitNewLeadAction(
    Map<String, dynamic> map,
  ) async {
    try {
      Response response = await dio.post(
        AppConstants.baseUrl + ApiConstants.leadsAdd,
        data: map,
      );
      return ApiResponse.completed(response.data);
    } on DioException catch (e) {
      if (e.type == DioExceptionType.receiveTimeout) {
        return ApiResponse.error(AppConstants.noInternet);
      } else if (e.response != null) {
        return ApiResponse.error(e.response?.data['message']);
      } else {
        return ApiResponse.error((e.error as SocketException).message);
      }
    }
  }

  Future<ApiResponse<dynamic>> getCreateLeadList() async {
    dio.options.headers["authorization"] =
        "Bearer ${AppConstants.currentProfile.token ?? " "}";
    try {
      Response response = await dio.get(
        AppConstants.baseUrl + ApiConstants.getAllCreatedLeads,
      );
      return ApiResponse.completed(response.data);
    } on DioException catch (e) {
      if (e.type == DioExceptionType.receiveTimeout) {
        return ApiResponse.error(AppConstants.noInternet);
      } else if (e.response != null) {
        return ApiResponse.error(e.response?.data['message']);
      } else {
        return ApiResponse.error((e.error as SocketException).message);
      }
    }
  }

  Future<ApiResponse<dynamic>> getAssignedLeadList() async {
    dio.options.headers["authorization"] =
        "Bearer ${AppConstants.currentProfile.token ?? " "}";
    try {
      Response response = await dio.get(
        AppConstants.baseUrl + ApiConstants.getAllAssinedLeads,
      );
      return ApiResponse.completed(response.data);
    } on DioException catch (e) {
      if (e.type == DioExceptionType.receiveTimeout) {
        return ApiResponse.error(AppConstants.noInternet);
      } else if (e.response != null) {
        return ApiResponse.error(e.response?.data['message']);
      } else {
        return ApiResponse.error((e.error as SocketException).message);
      }
    }
  }

  Future<ApiResponse<dynamic>> addRemarks(Map<String, dynamic> map) async {
    dio.options.headers["authorization"] =
        "Bearer ${AppConstants.currentProfile.token ?? " "}";
    try {
      print(map);
      Response response = await dio.patch(
        AppConstants.baseUrl + ApiConstants.addRemarks + map['id'],
        data: map,
      );
      return ApiResponse.completed(response.data);
    } on DioException catch (e) {
      if (e.type == DioExceptionType.receiveTimeout) {
        return ApiResponse.error(AppConstants.noInternet);
      } else if (e.response != null) {
        return ApiResponse.error(e.response?.data['message']);
      } else {
        return ApiResponse.error((e.error as SocketException).message);
      }
    }
  }

  Future<ApiResponse<dynamic>> sendOtpAction(Map<String, dynamic> map) async {
    try {
      Response response = await dio.post(
        AppConstants.baseUrl + ApiConstants.otpSend,
        data: map,
      );
      return ApiResponse.completed(response.data);
    } on DioException catch (e) {
      if (e.type == DioExceptionType.receiveTimeout) {
        return ApiResponse.error(AppConstants.noInternet);
      } else if (e.response != null) {
        return ApiResponse.error(e.response?.data['message']);
      } else {
        return ApiResponse.error((e.error as SocketException).message);
      }
    }
  }

  // Future<ApiResponse<GetAllTicketsModel>> getAllTicketsList(String page,String sValue,String sField) async {
  //   try {
  //     Response response = await dio
  //         .get("${AppConstants.baseUrl}${ApiConstants.getAllTickets}$page&searchValue=$sValue&searchField=$sField");
  //     GetAllTicketsModel objData = GetAllTicketsModel.fromJson(response.data);
  //     return ApiResponse.completed(objData);
  //   } on DioException catch (e) {
  //     if (e.type == DioExceptionType.receiveTimeout) {
  //       return ApiResponse.error(AppConstants.noInternet);
  //     } else {
  //       return ApiResponse.error(e.message);
  //     }
  //   }
  // }
}
