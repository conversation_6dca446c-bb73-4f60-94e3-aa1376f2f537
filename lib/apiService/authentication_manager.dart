import 'dart:convert';

import 'package:get/get.dart';
import 'package:kataria/globle/Constants.dart';

import 'cache_manager.dart';
import 'model/profileDetailsModel.dart';

class AuthenticationManager extends GetxController with CacheManager {
  final isLogged = false.obs;

  void logOut() {
    isLogged.value = false;
    removeToken();
  }

  void login(String? token) async {
    isLogged.value = true;
    await saveToken(token);
  }

  Future<void> checkLoginStatus() async {
    try {
      final token = await getToken();
      if (token != null) {
        Map<String, dynamic> data = jsonDecode(token);
        ProfileDetailsModel profileDetailsModel = ProfileDetailsModel.fromJson(
          data,
        );
        AppConstants.currentProfile = profileDetailsModel.data!;
        isLogged.value = true;
      }
    } catch (e) {
      isLogged.value = false;
      rethrow;
    }
  }
}
