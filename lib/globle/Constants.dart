import "package:flutter/material.dart";

import "../apiService/model/profileDetailsModel.dart";

abstract class ApiConstants {
  static const String verifyOtp = "api/v1/authentication/verify-otp";
  static const String otpSend = "api/v1/authentication/login";
  static const String logout = "api/v1/authentication/logout";
  static const String signUp = "api/v1/register-user/employee";
  static const String deviceType = "api/v1/register-user/device-type";
  static const String company = "api/v1/company/mobile";
  static const String branches = "api/v1/city/branch?sortBy=name&direction=asc";
  static const String products = "api/v1/company/sub-company/merged-products/";
  static const String getAllCreatedLeads = "api/v1/leads/get-all-created-leads";
  static const String getAllAssinedLeads = "api/v1/leads/get-all-assined-leads";
  static const String leadsAdd = "api/v1/leads/create";
  // static const String signOut = "sign-out";
  static const String announcements = "api/v1/announcements/mobile";
  static const String getAllNotification =
      "api/v1/notification/get-all-notification";
  static const String addRemarks = "api/v1/leads/";
}

abstract class AppConstants {
  static const String appName = "Kataria";
  static String baseUrl = "https://konnect-api.katariagroup.net/";
  // static String baseUrl = "https://be-konnect.globalvoxprojects.com/";
  static const String noInternet = "Internet is not connected.";
  static const String tokenExpierd =
      "Your login session expired, please re login your account.";
  static String userData = "user_data";
  static String fcmToken = "";
  static String diviceId = "";
  static ProfileDetails currentProfile = ProfileDetails();
}

abstract class AppColors {
  static const Color primaryBackground = Color(0xFFFFFFFF);
  static const Color secondaryBackground = Color(0xFFF5F5F5);
  static const Color primaryOpacity20 = Color(0xffFFC7A1);
  static const Color primaryElement = Color(0xFFECB141);
  static const Color addressElement = Color(0xFF7B7979);
  static const Color accentElement = Color(0xFF92C75E);
  static const Color titleText = Color(0xFF231F20);
  static const Color blackText = Color(0xFF000000);
  static const Color secondaryText = Color(0xFFDADADA);
  static const Color secondaryElemunt = Color(0xFFE9E9E9);
  static const Color tabbarText = Color(0xFF707070);
  static const Color grayText = Color(0xFFA7A5A6);
  static const Color greyLightText = Color(0xFF3c3c3c);
  static const Color blueCard = Color(0xFF0b6dd9);
  static const Color yellowCard = Color(0xFFedb01b);
  static const Color pinkCard = Color(0xFFbe4af4);
  static const Color blackCard = Color(0xFF101928);
  static const Color greenCard = Color(0xFF00a69c);
  static const Color primaryOpacity = Color(0xFFF47B54);
  static const Color green = Color(0xff217147);
}

class AppFonts {
  static const sFProRegular = "SFProRegular";
  static const sFProLight = "SFProLight";
  static const sFProSemibold = "SFProSemibold";
  static const sFProBold = "SFProBold";
  static const sFProHeavy = "SFProHeavy";
  static const sFProBlack = "SFProBlack";
  static const sFProMedium = "SFProMedium";
  static const sFProThin = "SFProThin";
}

class AppTypo {
  static const TextStyle appBarTitle = TextStyle(
    fontFamily: AppFonts.sFProMedium,
    fontWeight: FontWeight.w500,
    fontSize: 20,
  );
}
