import 'package:flutter/material.dart';

class AppTextStyles {
  static const String fontFamilyName = 'SFProRegular';

  static TextStyle regular(
          {double fontSize = 14}) =>
      TextStyle(
        fontSize: fontSize,
        fontWeight: FontWeight.w400,
        fontFamily: fontFamilyName,
      );

  static TextStyle medium(
          {double fontSize = 16}) =>
      TextStyle(
        fontSize: fontSize,
        fontWeight: FontWeight.w500,
        fontFamily: fontFamilyName,
      );

  static TextStyle bold(
          {double fontSize = 16}) =>
      TextStyle(
        fontSize: fontSize,
        fontWeight: FontWeight.w700,
        fontFamily: fontFamilyName,
      );
  static TextStyle light(
      {double fontSize = 14, FontWeight? fontWeight}) =>
      TextStyle(
        fontSize: fontSize,
        fontWeight: fontWeight?? FontWeight.w400,
        fontFamily: fontFamilyName,
      );
}
