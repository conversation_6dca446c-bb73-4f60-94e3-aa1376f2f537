// import 'package:connectivity_plus/connectivity_plus.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import '../main.dart';
// // import 'new_version.dart';
// import 'noNetwork_widget.dart';

// class NetworkController extends GetxController {
//   final Connectivity _connectivity = Connectivity();
//   final RxBool isConnected = true.obs;
//   bool _isInitialized = false;

//   @override
//   Future<void> onInit() async {
//     super.onInit();
//     await _initializeConnectivity();
//     _setupConnectivityListener();
//     // final newVersion = NewVersion(
//     //   // androidId: 'com.google.android.apps.cloudconsole',
//     //   androidId: 'com.goodlii',
//     //   iOSId: 'com.goodlii',
//     // );
//     // checkVersion(newVersion);
//   }

//   void _setupConnectivityListener() {
//     _connectivity.onConnectivityChanged.listen((ConnectivityResult result) {
//       _handleConnectivityResult(result);
//     });
//   }

//   Future<void> _initializeConnectivity() async {
//     if (_isInitialized) return;
//     try {
//       final result = await _connectivity.checkConnectivity();
//       _handleConnectivityResult(result);
//       _isInitialized = true;
//     } catch (e) {
//       debugPrint('Error checking connectivity: $e');
//       isConnected.value = false;
//     }
//   }

//   void _handleConnectivityResult(ConnectivityResult result) {
//     final bool wasConnected = isConnected.value;
//     isConnected.value = result != ConnectivityResult.none;

//     // Only show no network page if we're transitioning from connected to disconnected
//     if (wasConnected && !isConnected.value) {
//       _showNoNetworkPage();
//     }
//   }

//   void _showNoNetworkPage() {
//     final context = navigatorKey.currentContext;
//     if (context != null) {
//       Navigator.push(
//         context,
//         MaterialPageRoute(builder: (context) => const NoNetworkPage()),
//       );
//     }
//   }

//   // Future<void> checkVersion(NewVersion newVersion) async {
//   //   final status = await newVersion.getVersionStatus();
//   //   if (status != null) {
//   //     debugPrint(status.releaseNotes);
//   //     debugPrint(status.appStoreLink);
//   //     debugPrint(status.localVersion);
//   //     debugPrint(status.storeVersion);
//   //     debugPrint(status.canUpdate.toString());
//   //     if (status.canUpdate == true) {
//   //       newVersion.showUpdateDialog(
//   //         context: navigatorKey.currentContext!,
//   //         versionStatus: status,
//   //         allowDismissal: false,
//   //         updateButtonText: "Update Now",
//   //         dialogTitle: 'App Update Available',
//   //         dialogText:
//   //         'Looks like you have an older version of the app. Please Update to get latest features and best experience',
//   //       );
//   //     }
//   //   }
//   // }

//   @override
//   void onClose() {
//     _isInitialized = false;
//     super.onClose();
//   }
// }
