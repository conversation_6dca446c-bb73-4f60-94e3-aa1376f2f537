// import 'package:flutter/material.dart';
// import 'package:connectivity_plus/connectivity_plus.dart';

// class NoNetworkPage extends StatefulWidget {
//   const NoNetworkPage({super.key});

//   @override
//   State<NoNetworkPage> createState() => _NoNetworkPageState();
// }

// class _NoNetworkPageState extends State<NoNetworkPage> {
//   final Connectivity _connectivity = Connectivity();

//   @override
//   void initState() {
//     super.initState();
//     _connectivity.onConnectivityChanged.listen((ConnectivityResult result) {
//       _updateConnectionStatus(result);
//     });
//   }

//   void _updateConnectionStatus(ConnectivityResult result) {
//     if (result != ConnectivityResult.none && mounted) {
//       Navigator.pop(context);
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return WillPopScope(
//       onWillPop: () async {
//         final connectivityResult = await _connectivity.checkConnectivity();
//         return connectivityResult != ConnectivityResult.none;
//       },
//       child: Scaffold(
//         // appBar: AppBar(),
//         body: Container(
//           height: 10,
//           width: 10,
//           color: Colors.white,
//           child: Column(
//             children: [
//               SizedBox(height: 20),
//               Image.asset("assets/images/noInternet.gif"),
//               //Image.asset("assets/images/notifications.png"),
//               const SizedBox(height: 10),
//               // Text(
//               //   Languages.of(context)!.oopsText,
//               //   style: Theme.of(context).textTheme.headline6,
//               // ),
//               const SizedBox(height: 10),
//               // Padding(
//               //   padding: const EdgeInsets.symmetric(horizontal: 20.0),
//               //   child: Text(
//               //     Languages.of(context)!.noInternetText,
//               //     maxLines: 2,
//               //     textAlign: TextAlign.center,
//               //     style: Theme.of(context).textTheme.headline6,
//               //   ),
//               // ),
//               const SizedBox(height: 20),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
