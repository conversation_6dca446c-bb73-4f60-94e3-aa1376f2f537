import 'package:flutter/material.dart';
import 'package:kataria/apiService/api_response.dart';

import '../apiService/authentication_manager.dart';
import 'package:get/get.dart';

import '../apiService/jsonPlacehoderProvider.dart';
import 'Constants.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

class LogoutBtn extends StatefulWidget {
  const LogoutBtn({super.key});

  @override
  State<LogoutBtn> createState() => _LogoutBtnState();
}

class _LogoutBtnState extends State<LogoutBtn> {
  final AuthenticationManager _authManager = Get.find();
  final JsonPlaceholerProvider _repository = JsonPlaceholerProvider();

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: () {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Logout'),
              content: const Text('Are you sure you want to logout?'),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(); // Close the dialog
                  },
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(); // Close the dialog
                    _authManager.logOut();
                    logoutApiCall();
                  },
                  child: const Text('Logout'),
                ),
              ],
            );
          },
        );
      },
      icon: const Icon(Icons.logout),
    );
  }

  logoutApiCall() async {
    try {
      var map = <String, dynamic>{};
      map["fcmToken"] = AppConstants.fcmToken;

      // Log the FCM token being sent for logout
      debugPrint('Sending FCM token for logout: ${AppConstants.fcmToken}');

      ApiResponse<dynamic> model = await _repository.logoutAction(map);
      if (model.status == Status.COMPLETED) {
        if (!mounted) return;

        // Clear FCM token after successful logout
        AppConstants.fcmToken = '';

        // Unsubscribe from topics
        try {
          await FirebaseMessaging.instance.unsubscribeFromTopic('lead-updates');
          await FirebaseMessaging.instance.unsubscribeFromTopic('news-updates');
        } catch (e) {
          debugPrint('Error unsubscribing from topics: $e');
        }
      } else {
        Get.defaultDialog(
          title: "",
          middleText: model.message ?? '',
          textConfirm: "OK",
          buttonColor: AppColors.primaryElement,
          titleStyle: const TextStyle(fontSize: 0),
          onConfirm: () => Get.back(),
        );
      }
    } catch (e) {
      Get.defaultDialog(
        content: const Text('User not found!'),
        textConfirm: 'OK',
        titleStyle: const TextStyle(fontSize: 0),
        middleText: "",
        confirmTextColor: Colors.black,
        onConfirm: () {
          Get.back();
        },
      );
    }
  }
}
