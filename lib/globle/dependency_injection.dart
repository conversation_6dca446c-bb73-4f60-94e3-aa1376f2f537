// import 'package:get/get.dart';
// import 'network_controller.dart';

// class DependencyInjection {
//   static Future<void> init() async {
//     try {
//       // Initialize network controller
//       await Get.putAsync<NetworkController>(
//         () async => NetworkController(),
//         permanent: true,
//       );
//     } catch (e) {
//       print('Error initializing dependencies: $e');
//       rethrow;
//     }
//   }
// }
