name: kataria
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+2

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  firebase_core: ^3.13.1
  firebase_messaging: ^15.2.6
  get: ^4.6.6
  shimmer: ^3.0.0
  progress_loading_button: ^2.0.2
  get_storage: ^2.1.1
  flutter_staggered_grid_view: ^0.7.0
  flutter_bloc: ^8.1.6
  sms_autofill: ^2.4.1
  dio: ^5.5.0+1
  cached_network_image: ^3.3.1
  flutter_local_notifications: ^17.2.1+2
  # connectivity_plus: ^5.0.2
  dropdown_search: ^5.0.6
  drop_down_search_field: ^1.0.4
  flutter_widget_from_html: ^0.15.2
  flutter_secure_storage: ^9.2.2
  platform_device_id_plus: ^1.0.6
  webview_flutter: ^4.10.0
  intl: ^0.20.0
  url_launcher: ^6.3.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_launcher_icons: ^0.13.1
  flutter_native_splash: ^2.4.1
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: 5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/fonts/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: SFProRegular
      fonts:
        - asset: assets/fonts/SF-Pro-Display-Regular.otf
    - family: SFProLight
      fonts:
        - asset: assets/fonts/SF-Pro-Display-Light.otf
    - family: SFProSemibold
      fonts:
        - asset: assets/fonts/SF-Pro-Display-Semibold.otf
    - family: SFProBold
      fonts:
        - asset: assets/fonts/SF-Pro-Display-Bold.otf
    - family: SFProHeavy
      fonts:
        - asset: assets/fonts/SF-Pro-Display-Heavy.otf
    - family: SFProBlack
      fonts:
        - asset: assets/fonts/SF-Pro-Display-Black.otf
    - family: SFProMedium
      fonts:
        - asset: assets/fonts/SF-Pro-Display-Medium.otf
    - family: SFProThin
      fonts:
        - asset: assets/fonts/SF-Pro-Display-Thin.otf
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/appIcon.png"
  min_sdk_android: 21 # android min sdk min:16, default 21

flutter_native_splash:
  color: "#F7F7F7"
  image: assets/images/appIcon.png
  color_dark: "#F7F7F7"
  image_dark: assets/images/appIcon.png
  android_12:
    #    image: assets/images/appIcon.png
    icon_background_color: "#F7F7F7"
    #    image_dark: assets/images/appIcon.png
    icon_background_color_dark: "#F7F7F7"
    color: "#F7F7F7"
