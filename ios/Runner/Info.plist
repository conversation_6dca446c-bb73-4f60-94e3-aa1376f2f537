<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>dev.flutter.background.refresh</string>
		<string>dev.flutter.background.processing</string>
	</array>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Kataria</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>kataria</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSMicrophoneUsageDescription</key>
	<string>This app needs microphone access for voice messages and calls</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>processing</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>NSPrivacyManifest</key>
	<dict>
		<key>NSPrivacyAccessedAPITypes</key>
		<array>
			<dict>
				<key>NSPrivacyAccessedAPIType</key>
				<string>NSUserDefaults</string>
				<key>NSPrivacyAccessedAPITypeReasons</key>
				<array>
					<string>C617.1</string>
				</array>
			</dict>
		</array>
		<key>NSPrivacyTracking</key>
		<false/>
		<key>NSPrivacyTrackingDomains</key>
		<array/>
		<key>NSPrivacyCollectedDataTypes</key>
		<array/>
		<key>NSPrivacyAccessedAPITypeReasons</key>
		<dict>
			<key>C617.1</key>
			<string>This app uses NSUserDefaults to store app preferences and settings.</string>
		</dict>
	</dict>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>This app needs camera access to take photos and videos</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app needs photo library access to select and upload photos</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app needs location access to provide location-based services</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>This app needs location access to provide location-based services even when in background</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>This app needs permission to save photos to your photo library</string>
	<key>NSFaceIDUsageDescription</key>
	<string>This app needs Face ID access for secure authentication</string>
	<key>NSContactsUsageDescription</key>
	<string>This app needs contacts access to help you connect with your contacts</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>This app needs Bluetooth access for connecting to nearby devices</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>This app needs Bluetooth access for connecting to nearby devices</string>
	<key>NSLocalNetworkUsageDescription</key>
	<string>This app needs local network access to discover and connect to nearby devices</string>
	<key>NSBonjourServices</key>
	<array>
		<string>_http._tcp</string>
		<string>_https._tcp</string>
	</array>
</dict>
</plist>
